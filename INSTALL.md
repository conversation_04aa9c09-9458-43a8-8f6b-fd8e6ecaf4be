# Money Extension 安装指南

## 🚀 快速安装

### 步骤 1: 下载项目
```bash
git clone https://github.com/your-username/money-extension.git
cd money-extension
```

### 步骤 2: 生成图标（可选）
1. 在浏览器中打开 `generate_icons.html`
2. 调整图标颜色和样式
3. 点击"下载全部"按钮
4. 将下载的图标文件重命名并放入 `assets/icons/` 目录：
   - `icon16.png`
   - `icon48.png`
   - `icon128.png`

### 步骤 3: 安装Chrome扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录 `money-extension`
6. 确认安装成功，插件图标出现在工具栏

## 🧪 功能测试

### 测试东方财富功能

#### 方法一：使用简化测试页面（推荐）
1. 在浏览器中打开 `test_simple.html`
2. 点击"添加前10率列"按钮
3. 点击"计算前10率数据"按钮
4. 观察表格变化和日志输出

#### 方法二：访问真实网站
1. 访问 [东方财富人气榜](https://guba.eastmoney.com/rank/)
2. 等待页面完全加载
3. 查看表格中是否添加了"前10率"列
4. 按F12查看控制台日志

### 验证插件状态
1. 点击Chrome工具栏中的插件图标
2. 查看弹窗中显示的状态信息
3. 确认当前页面是否匹配对应模块

## 🔧 故障排除

### 常见问题

#### 1. 插件无法加载
**症状**: 插件图标不显示或显示错误
**解决方案**:
- 确认已开启开发者模式
- 检查manifest.json语法是否正确
- 重新加载扩展程序

#### 2. 功能不生效
**症状**: 访问目标网站时没有添加新功能
**解决方案**:
- 按F12查看控制台是否有错误信息
- 确认URL匹配模式是否正确
- 检查网站页面结构是否发生变化

#### 3. 模块加载失败
**症状**: 控制台显示"Module class not found"错误
**解决方案**:
- 检查模块文件是否存在
- 确认模块类名是否正确导出
- 重新安装扩展程序

### 调试技巧

#### 查看详细日志
```javascript
// 在控制台中执行以下命令查看插件状态
console.log(window.moneyExtension);
console.log(window.EastmoneyRankModule);
```

#### 手动触发功能
```javascript
// 手动重新加载模块
if (window.moneyExtension) {
    window.moneyExtension.loadModuleForCurrentPage();
}
```

## 📋 支持的网站

### ✅ 已支持
- **东方财富人气榜**: https://guba.eastmoney.com/rank/
  - 功能：添加"前10率"列
  - 状态：✅ 已完成

### 🔄 开发中
- **财联社热榜**: https://api3.cls.cn/quote/toplist
  - 功能：点击跳转增强
  - 状态：🔄 计划中

## 🆕 版本更新

### 更新插件
1. 下载最新版本代码
2. 在 `chrome://extensions/` 页面点击插件的"重新加载"按钮
3. 或者删除旧版本，重新安装新版本

### 版本历史
- **v1.0.0**: 基础架构 + 东方财富前10率功能
- **v1.1.0**: 计划添加财联社功能

## 💡 使用技巧

### 东方财富前10率功能
- **颜色含义**:
  - 🟢 绿色 (≥80%): 优秀，经常在前100
  - 🟡 黄色 (60-79%): 良好，较常在前100
  - 🟠 橙色 (40-59%): 一般，偶尔在前100
  - 🔴 红色 (<40%): 较差，很少在前100

- **数据说明**:
  - 基于历史趋势列的排名数据计算
  - 统计过去10天内排名前100的天数比例
  - 鼠标悬停可查看详细信息

## 📞 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查控制台错误信息
3. 通过GitHub Issues报告问题

---

**开发者**: shenjing023  
**邮箱**: <EMAIL>  
**项目地址**: https://github.com/your-username/money-extension
