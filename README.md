# Money Extension - 金融网站增强插件

一个专为金融网站设计的Chrome扩展插件，为东方财富人气榜和财联社热榜提供额外的功能增强。

## 🚀 功能特性

### ✅ 已实现功能

#### 1. 东方财富个股人气榜增强
- **前10率统计**：在人气榜表格中添加"前10率"列
- **智能计算**：基于历史趋势数据计算过去10天内排名前100的天数比例
- **可视化显示**：使用颜色编码显示不同的前10率水平
  - 🟢 绿色 (≥80%)：优秀
  - 🟡 黄色 (60-79%)：良好
  - 🟠 橙色 (40-59%)：一般
  - 🔴 红色 (<40%)：较差

#### 2. 模块化架构
- **URL路由系统**：自动检测页面并加载对应功能模块
- **动态加载**：模块按需加载，提高性能
- **生命周期管理**：完整的模块初始化和销毁机制

### 🔄 计划中功能

#### 财联社热榜增强
- 添加点击跳转功能
- 热榜数据分析

## 📦 安装方法

### 方式一：开发者模式安装（推荐）

1. **下载源码**
   ```bash
   git clone https://github.com/your-username/money-extension.git
   cd money-extension
   ```

2. **生成图标**（可选）
   - 打开 `generate_icons.html` 文件
   - 调整图标样式和颜色
   - 下载生成的图标文件
   - 将图标文件放入 `assets/icons/` 目录

3. **安装插件**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录

4. **验证安装**
   - 插件图标应出现在Chrome工具栏
   - 点击图标查看插件状态

## 🧪 测试方法

### 测试东方财富功能

1. **使用测试页面**
   - 打开项目中的 `test_eastmoney.html` 文件
   - 观察表格中是否添加了"前10率"列
   - 检查数据计算和颜色显示是否正确

2. **访问真实网站**
   - 访问 [东方财富人气榜](https://guba.eastmoney.com/rank/)
   - 等待页面加载完成
   - 查看是否在表格中添加了"前10率"列

### 调试方法

1. **查看控制台日志**
   - 按F12打开开发者工具
   - 切换到Console标签
   - 查看插件的日志输出

2. **检查插件状态**
   - 点击插件图标
   - 查看弹窗中的状态信息
   - 确认当前页面是否匹配对应模块

## 🏗️ 项目结构

```
money-extension/
├── manifest.json              # Chrome插件配置文件
├── content/                   # 内容脚本
│   ├── content.js            # 主入口文件，负责路由分发
│   └── modules/              # 功能模块
│       ├── eastmoney_rank.js # 东方财富人气榜模块
│       └── cls_rank.js       # 财联社热榜模块
├── popup/                    # 插件弹窗界面
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── utils/                    # 工具函数库
│   └── common.js
├── assets/                   # 静态资源
│   └── icons/               # 插件图标
├── generate_icons.html       # 图标生成器
├── test_eastmoney.html      # 东方财富功能测试页面
└── README.md
```

## 🔧 开发说明

### 添加新功能模块

1. **创建模块文件**
   ```javascript
   // content/modules/new_module.js
   class NewModule {
     constructor() {
       this.moduleName = 'NewModule';
       this.urlPatterns = ['*://example.com/*'];
     }

     async init() { /* 初始化逻辑 */ }
     async destroy() { /* 清理逻辑 */ }
   }

   window.NewModule = NewModule;
   ```

2. **注册模块**
   在 `content/content.js` 的 `moduleConfigs` 中添加配置：
   ```javascript
   {
     name: 'new_module',
     patterns: ['*://example.com/*'],
     scriptPath: 'content/modules/new_module.js'
   }
   ```

3. **更新权限**
   在 `manifest.json` 中添加对应的URL权限。

### 调试技巧

- 使用 `CommonUtils.log()` 输出调试信息
- 在开发者工具中查看Network标签，确认脚本加载
- 使用Elements标签检查DOM修改是否正确

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 实现基础模块化架构
- ✅ 完成东方财富人气榜"前10率"功能
- ✅ 添加插件弹窗界面
- ✅ 创建图标生成器
- ✅ 建立测试页面

### 计划中的更新
- 🔄 财联社热榜跳转功能
- 🔄 数据持久化存储
- 🔄 用户设置界面
- 🔄 更多金融网站支持

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证，详见 [LICENSE](LICENSE) 文件。

## 👨‍💻 作者

**shenjing023** - [<EMAIL>](mailto:<EMAIL>)

---

如有问题或建议，请通过GitHub Issues联系我们。