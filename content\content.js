/**
 * Money Extension - 主入口文件
 * 负责URL路由分发和模块管理
 */

class MoneyExtension {
  constructor() {
    this.currentModule = null;
    this.modules = new Map();
    this.isInitialized = false;
    
    // 模块配置
    this.moduleConfigs = [
      {
        name: 'eastmoney_rank',
        patterns: [
          '*://guba.eastmoney.com/rank/*',
          'file://*/test_eastmoney.html*',
          '*test_eastmoney.html*'
        ],
        scriptPath: 'content/modules/eastmoney_rank.js'
      },
      {
        name: 'cls_rank',
        patterns: [
          '*://api3.cls.cn/quote/toplist*'
        ],
        scriptPath: 'content/modules/cls_rank.js'
      }
    ];
    
    CommonUtils.log('info', 'Core', 'Money Extension initialized');
  }

  /**
   * 初始化插件
   */
  async init() {
    if (this.isInitialized) {
      CommonUtils.log('warn', 'Core', 'Extension already initialized');
      return;
    }

    try {
      CommonUtils.log('info', 'Core', 'Starting extension initialization');
      
      // 检测当前页面URL并加载对应模块
      await this.loadModuleForCurrentPage();
      
      // 监听页面变化（SPA应用支持）
      this.setupPageChangeListener();
      
      this.isInitialized = true;
      CommonUtils.log('info', 'Core', 'Extension initialization completed');
      
    } catch (error) {
      CommonUtils.log('error', 'Core', 'Extension initialization failed', error);
    }
  }

  /**
   * 为当前页面加载对应的模块
   */
  async loadModuleForCurrentPage() {
    const currentUrl = window.location.href;
    CommonUtils.log('info', 'Core', `Checking modules for URL: ${currentUrl}`);

    // 查找匹配的模块配置
    const matchedConfig = this.moduleConfigs.find(config => 
      CommonUtils.matchUrl(currentUrl, config.patterns)
    );

    if (matchedConfig) {
      CommonUtils.log('info', 'Core', `Found matching module: ${matchedConfig.name}`);
      await this.loadModule(matchedConfig);
    } else {
      CommonUtils.log('info', 'Core', 'No matching module found for current URL');
    }
  }

  /**
   * 动态加载模块
   * @param {Object} config - 模块配置
   */
  async loadModule(config) {
    try {
      // 如果已有模块在运行，先销毁
      if (this.currentModule) {
        await this.unloadCurrentModule();
      }

      CommonUtils.log('info', 'Core', `Loading module: ${config.name}`);

      // 获取模块类（模块已在manifest中预加载）
      const expectedClassName = this.getModuleClassName(config.name);
      CommonUtils.log('info', 'Core', `Looking for module class: ${expectedClassName}`);

      const ModuleClass = window[expectedClassName];
      if (!ModuleClass) {
        CommonUtils.log('error', 'Core', `Available window properties:`, Object.keys(window).filter(key => key.includes('Module')));
        throw new Error(`Module class not found: ${expectedClassName} for module ${config.name}`);
      }

      CommonUtils.log('info', 'Core', `Found module class: ${expectedClassName}`);

      // 实例化并初始化模块
      const moduleInstance = new ModuleClass();
      await moduleInstance.init();

      // 保存模块实例
      this.currentModule = {
        name: config.name,
        instance: moduleInstance,
        config: config
      };

      this.modules.set(config.name, this.currentModule);
      CommonUtils.log('info', 'Core', `Module ${config.name} loaded successfully`);

    } catch (error) {
      CommonUtils.log('error', 'Core', `Failed to load module: ${config.name}`, error);
    }
  }

  /**
   * 卸载当前模块
   */
  async unloadCurrentModule() {
    if (!this.currentModule) return;

    try {
      CommonUtils.log('info', 'Core', `Unloading module: ${this.currentModule.name}`);
      
      if (this.currentModule.instance && typeof this.currentModule.instance.destroy === 'function') {
        await this.currentModule.instance.destroy();
      }

      this.currentModule = null;
      CommonUtils.log('info', 'Core', 'Module unloaded successfully');

    } catch (error) {
      CommonUtils.log('error', 'Core', 'Failed to unload module', error);
    }
  }

  /**
   * 动态加载脚本
   * @param {string} scriptPath - 脚本路径
   */
  loadScript(scriptPath) {
    return new Promise((resolve, reject) => {
      // 检查脚本是否已加载
      if (document.querySelector(`script[src*="${scriptPath}"]`)) {
        CommonUtils.log('info', 'Core', `Script already loaded: ${scriptPath}`);
        resolve();
        return;
      }

      const script = document.createElement('script');

      // 检查是否在Chrome扩展环境中
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getURL) {
        script.src = chrome.runtime.getURL(scriptPath);
      } else {
        // 测试环境或非扩展环境，使用相对路径
        script.src = scriptPath;
        CommonUtils.log('warn', 'Core', `Loading script in test mode: ${scriptPath}`);
      }

      script.onload = () => {
        CommonUtils.log('info', 'Core', `Script loaded successfully: ${scriptPath}`);
        resolve();
      };

      script.onerror = (error) => {
        CommonUtils.log('error', 'Core', `Failed to load script: ${scriptPath}`, error);
        reject(new Error(`Failed to load script: ${scriptPath}`));
      };

      document.head.appendChild(script);
      CommonUtils.log('info', 'Core', `Loading script: ${script.src}`);
    });
  }

  /**
   * 获取模块类名
   * @param {string} moduleName - 模块名称
   * @returns {string} 类名
   */
  getModuleClassName(moduleName) {
    // 将 snake_case 转换为 PascalCase
    return moduleName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('') + 'Module';
  }

  /**
   * 设置页面变化监听器（支持SPA）
   */
  setupPageChangeListener() {
    let lastUrl = window.location.href;

    // 监听 pushState 和 replaceState
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      window.dispatchEvent(new Event('urlchange'));
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      window.dispatchEvent(new Event('urlchange'));
    };

    // 监听 popstate 和自定义 urlchange 事件
    ['popstate', 'urlchange'].forEach(eventType => {
      window.addEventListener(eventType, CommonUtils.debounce(async () => {
        const currentUrl = window.location.href;
        if (currentUrl !== lastUrl) {
          CommonUtils.log('info', 'Core', `URL changed from ${lastUrl} to ${currentUrl}`);
          lastUrl = currentUrl;
          await this.loadModuleForCurrentPage();
        }
      }, 100));
    });
  }

  /**
   * 销毁插件
   */
  async destroy() {
    CommonUtils.log('info', 'Core', 'Destroying extension');
    
    await this.unloadCurrentModule();
    this.modules.clear();
    this.isInitialized = false;
  }
}

// 创建全局实例
const moneyExtension = new MoneyExtension();

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => moneyExtension.init());
} else {
  moneyExtension.init();
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => moneyExtension.destroy());
