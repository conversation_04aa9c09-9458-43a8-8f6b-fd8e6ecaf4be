/**
 * 财联社热榜模块
 * 负责在财联社热榜API页面添加点击跳转功能
 */

class ClsRankModule {
  constructor() {
    this.moduleName = 'ClsRank';
    this.isActive = false;
    this.observers = [];
    this.eventListeners = [];
    
    // URL匹配模式
    this.urlPatterns = [
      '*://api3.cls.cn/quote/toplist*',
      'file://*/test_cls.html*',  // 支持测试页面
      '*test_cls.html*'           // 支持各种测试环境
    ];
    
    CommonUtils.log('info', this.moduleName, 'Module instance created');
  }

  /**
   * 检查当前URL是否匹配此模块
   * @param {string} url - 要检查的URL，默认为当前页面URL
   * @returns {boolean} 是否匹配
   */
  isMatch(url = window.location.href) {
    return CommonUtils.matchUrl(url, this.urlPatterns);
  }

  /**
   * 初始化模块
   */
  async init() {
    if (this.isActive) {
      CommonUtils.log('warn', this.moduleName, 'Module already active');
      return;
    }

    try {
      CommonUtils.log('info', this.moduleName, 'Initializing module');

      // 检查URL匹配
      if (!this.isMatch()) {
        throw new Error('URL does not match this module');
      }

      // 等待页面关键元素加载
      await this.waitForPageReady();

      // 初始化功能
      await this.initializeFeatures();

      this.isActive = true;
      CommonUtils.log('info', this.moduleName, 'Module initialized successfully');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Module initialization failed', error);
      throw error;
    }
  }

  /**
   * 等待页面准备就绪
   */
  async waitForPageReady() {
    CommonUtils.log('info', this.moduleName, 'Waiting for page to be ready');

    try {
      // 等待页面基本结构加载
      // 由于这是API页面，可能需要等待JSON数据渲染或特定元素
      await CommonUtils.waitForElement('body', 5000);
      
      // 额外等待一段时间确保动态内容加载完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      CommonUtils.log('info', this.moduleName, 'Page ready detected');

    } catch (error) {
      CommonUtils.log('warn', this.moduleName, 'Page ready detection timeout, proceeding anyway');
    }
  }

  /**
   * 初始化功能特性
   */
  async initializeFeatures() {
    CommonUtils.log('info', this.moduleName, 'Initializing features');

    // 功能1：设置点击跳转功能（具体实现在第二阶段）
    this.setupClickJumpFeature();

    // 功能2：设置页面监听器
    this.setupPageObservers();

    // 功能3：设置事件监听器
    this.setupEventListeners();

    CommonUtils.log('info', this.moduleName, 'Features initialized');
  }

  /**
   * 设置点击跳转功能
   * 为股票名称、现价、涨跌幅添加点击跳转到东方财富股吧的功能
   */
  setupClickJumpFeature() {
    CommonUtils.log('info', this.moduleName, 'Setting up click jump feature');

    try {
      // 处理财联社热榜页面
      this.setupClsPageHandlers();

      // 处理测试页面
      this.setupTestPageHandlers();

      CommonUtils.log('info', this.moduleName, 'Click jump feature setup completed');
    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Failed to setup click jump feature', error);
    }
  }

  /**
   * 处理财联社热榜页面的点击事件
   */
  setupClsPageHandlers() {
    // 查找所有热榜项目
    const hotListItems = document.querySelectorAll('.hot-list-item');

    CommonUtils.log('info', this.moduleName, `Found ${hotListItems.length} hot list items`);

    hotListItems.forEach(item => {
      // 从 data-openapplink 属性中提取股票代码
      const openAppElement = item.querySelector('[data-openapplink]');
      if (!openAppElement) return;

      const openAppLink = openAppElement.getAttribute('data-openapplink');
      const stockCode = this.extractStockCodeFromOpenAppLink(openAppLink);

      if (!stockCode) return;

      CommonUtils.log('info', this.moduleName, `Found stock code: ${stockCode} from ${openAppLink}`);

      // 查找可点击的元素：股票名称、现价、涨跌幅
      const clickableElements = item.querySelectorAll('.c-333, .c-666, .c-fb3e4e');

      clickableElements.forEach(element => {
        // 添加视觉提示
        element.style.cursor = 'pointer';

        // 添加点击事件
        element.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleStockClick(stockCode, element);
        });
      });
    });
  }

  /**
   * 处理测试页面的点击事件
   */
  setupTestPageHandlers() {
    const stockItems = document.querySelectorAll('.stock-item');

    stockItems.forEach(item => {
      const stockCode = item.getAttribute('data-stock-code');
      if (!stockCode) return;

      // 为股票名称、现价、涨跌幅添加点击事件
      const clickableElements = item.querySelectorAll('.stock-name, .stock-price, .stock-change');

      clickableElements.forEach(element => {
        // 添加视觉提示
        element.style.cursor = 'pointer';

        // 添加点击事件
        element.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleStockClick(stockCode, element);
        });
      });
    });

    CommonUtils.log('info', this.moduleName, `Setup handlers for ${stockItems.length} test page stock items`);
  }

  /**
   * 从 data-openapplink 属性中提取股票代码
   * @param {string} openAppLink - openapplink属性值
   * @returns {string|null} 股票代码
   */
  extractStockCodeFromOpenAppLink(openAppLink) {
    if (!openAppLink) return null;

    // 匹配 stock_id=sz002104 或 stock_id=sh600519 格式
    const match = openAppLink.match(/stock_id=(?:sz|sh)?(\d{6})/i);
    return match ? match[1] : null;
  }

  /**
   * 设置页面观察器
   */
  setupPageObservers() {
    CommonUtils.log('info', this.moduleName, 'Setting up page observers');

    // 监听DOM变化，处理动态加载的内容
    const observer = new MutationObserver(CommonUtils.throttle((mutations) => {
      this.handlePageChanges(mutations);
    }, 500));

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false
    });

    this.observers.push(observer);
    CommonUtils.log('info', this.moduleName, 'Page observers setup completed');
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    CommonUtils.log('info', this.moduleName, 'Setting up event listeners');

    // 页面点击事件监听器（用于跳转功能）
    const clickHandler = (event) => {
      this.handlePageClick(event);
    };

    document.addEventListener('click', clickHandler);
    this.eventListeners.push({
      element: document,
      event: 'click',
      handler: clickHandler
    });

    CommonUtils.log('info', this.moduleName, 'Event listeners setup completed');
  }

  /**
   * 处理页面变化
   * @param {MutationRecord[]} mutations - DOM变化记录
   */
  handlePageChanges(mutations) {
    // 检查是否有新的内容添加
    const hasNewContent = mutations.some(mutation => 
      mutation.addedNodes.length > 0
    );

    if (hasNewContent) {
      CommonUtils.log('info', this.moduleName, 'New content detected, refreshing features');
      // 重新初始化相关功能
      this.setupClickJumpFeature();
    }
  }

  /**
   * 处理页面点击事件
   * @param {Event} event - 点击事件
   */
  handlePageClick(event) {
    const target = event.target;
    CommonUtils.log('info', this.moduleName, 'Click detected', {
      tagName: target.tagName,
      className: target.className,
      textContent: target.textContent?.substring(0, 50)
    });

    // 这个方法现在主要用于调试和监控
    // 实际的点击处理已经在 setupClickJumpFeature 中实现
  }

  /**
   * 为元素设置点击处理器
   * @param {Element} element - 要设置处理器的元素
   * @param {string} stockCode - 股票代码
   */
  setupElementClickHandlers(element, stockCode) {
    // 查找可点击的子元素（股票名称、现价、涨跌幅等）
    const clickableSelectors = [
      '.stock-name', '.stock-price', '.stock-change',
      '[class*="name"]', '[class*="price"]', '[class*="change"]',
      'td:nth-child(2)', 'td:nth-child(3)', 'td:nth-child(4)', // 表格的第2、3、4列通常是名称、价格、涨跌幅
    ];

    let foundClickableElements = false;

    clickableSelectors.forEach(selector => {
      const elements = element.querySelectorAll(selector);
      elements.forEach(el => {
        if (this.isClickableStockElement(el)) {
          this.addClickHandler(el, stockCode);
          foundClickableElements = true;
        }
      });
    });

    // 如果没有找到特定的可点击元素，就为整个元素添加点击处理
    if (!foundClickableElements) {
      this.addClickHandler(element, stockCode);
    }
  }

  /**
   * 为元素添加点击处理器
   * @param {Element} element - 元素
   * @param {string} stockCode - 股票代码
   */
  addClickHandler(element, stockCode) {
    // 添加视觉提示
    element.style.cursor = 'pointer';

    // 添加点击事件
    element.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.handleStockClick(stockCode, element);
    });
  }

  /**
   * 处理股票点击事件
   * @param {string} stockCode - 股票代码
   * @param {Element} element - 被点击的元素
   */
  handleStockClick(stockCode, element) {
    CommonUtils.log('info', this.moduleName, `Stock clicked: ${stockCode}`, {
      elementText: element.textContent?.trim(),
      elementClass: element.className
    });

    // 构建东方财富股吧URL
    const eastmoneyUrl = `https://guba.eastmoney.com/list,${stockCode}.html`;

    CommonUtils.log('info', this.moduleName, `Redirecting to: ${eastmoneyUrl}`);

    // 在新标签页中打开
    window.open(eastmoneyUrl, '_blank');
  }

  /**
   * 从元素中提取股票代码
   * @param {Element} element - 元素
   * @returns {string|null} 股票代码
   */
  extractStockCode(element) {
    // 方法1: 从data属性中获取
    const dataCode = element.getAttribute('data-stock-code') ||
                    element.getAttribute('data-code') ||
                    element.getAttribute('data-symbol');

    if (dataCode) {
      return this.normalizeStockCode(dataCode);
    }

    // 方法2: 从文本内容中提取
    const textCode = this.extractStockCodeFromText(element.textContent || '');
    if (textCode) {
      return textCode;
    }

    // 方法3: 从子元素中查找
    const codeElement = element.querySelector('.stock-code, [class*="code"]');
    if (codeElement) {
      const code = this.extractStockCodeFromText(codeElement.textContent || '');
      if (code) return code;
    }

    return null;
  }

  /**
   * 从文本中提取股票代码
   * @param {string} text - 文本内容
   * @returns {string|null} 股票代码
   */
  extractStockCodeFromText(text) {
    if (!text) return null;

    // 匹配6位数字的股票代码（A股）
    const match = text.match(/\b([0-9]{6})\b/);
    return match ? match[1] : null;
  }

  /**
   * 标准化股票代码
   * @param {string} code - 原始代码
   * @returns {string} 标准化后的代码
   */
  normalizeStockCode(code) {
    if (!code) return '';

    // 移除非数字字符，保留6位数字
    const cleaned = code.replace(/\D/g, '');
    return cleaned.length === 6 ? cleaned : '';
  }

  /**
   * 判断元素是否应该是可点击的股票元素
   * @param {Element} element - 元素
   * @returns {boolean} 是否可点击
   */
  isClickableStockElement(element) {
    if (!element) return false;

    const text = element.textContent?.trim() || '';
    const className = element.className || '';

    // 检查是否包含股票相关的类名
    const stockRelatedClasses = ['stock', 'name', 'price', 'change', 'symbol'];
    const hasStockClass = stockRelatedClasses.some(cls =>
      className.toLowerCase().includes(cls)
    );

    // 检查是否包含中文字符（股票名称）
    const hasChinese = /[\u4e00-\u9fa5]/.test(text);

    // 检查是否包含价格格式（数字+小数点）
    const hasPrice = /^\d+\.\d+$/.test(text);

    // 检查是否包含涨跌幅格式（+/-百分比）
    const hasChange = /^[+-]\d+\.\d+%$/.test(text);

    return hasStockClass || hasChinese || hasPrice || hasChange;
  }

  /**
   * 销毁模块
   */
  async destroy() {
    if (!this.isActive) {
      CommonUtils.log('warn', this.moduleName, 'Module not active, nothing to destroy');
      return;
    }

    try {
      CommonUtils.log('info', this.moduleName, 'Destroying module');

      // 清理观察器
      this.observers.forEach(observer => observer.disconnect());
      this.observers = [];

      // 清理事件监听器
      this.eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      this.eventListeners = [];

      // 清理添加的DOM元素
      this.cleanupDOMElements();

      this.isActive = false;
      CommonUtils.log('info', this.moduleName, 'Module destroyed successfully');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Module destruction failed', error);
    }
  }

  /**
   * 清理DOM元素
   */
  cleanupDOMElements() {
    // 移除所有由此模块添加的DOM元素
    const elementsToRemove = document.querySelectorAll('[data-money-extension="cls-rank"]');
    elementsToRemove.forEach(element => element.remove());
    
    CommonUtils.log('info', this.moduleName, `Cleaned up ${elementsToRemove.length} DOM elements`);
  }

  /**
   * 获取模块状态信息
   * @returns {Object} 模块状态
   */
  getStatus() {
    return {
      moduleName: this.moduleName,
      isActive: this.isActive,
      urlPatterns: this.urlPatterns,
      observersCount: this.observers.length,
      eventListenersCount: this.eventListeners.length,
      currentUrl: window.location.href,
      isMatch: this.isMatch()
    };
  }
}

// 将模块类暴露到全局作用域，供主入口文件使用
window.ClsRankModule = ClsRankModule;
