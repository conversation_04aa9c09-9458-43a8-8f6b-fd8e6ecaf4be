/**
 * 财联社热榜模块
 * 负责在财联社热榜API页面添加点击跳转功能
 */

class ClsRankModule {
  constructor() {
    this.moduleName = 'ClsRank';
    this.isActive = false;
    this.observers = [];
    this.eventListeners = [];
    
    // URL匹配模式
    this.urlPatterns = [
      '*://api3.cls.cn/quote/toplist*'
    ];
    
    CommonUtils.log('info', this.moduleName, 'Module instance created');
  }

  /**
   * 检查当前URL是否匹配此模块
   * @param {string} url - 要检查的URL，默认为当前页面URL
   * @returns {boolean} 是否匹配
   */
  isMatch(url = window.location.href) {
    return CommonUtils.matchUrl(url, this.urlPatterns);
  }

  /**
   * 初始化模块
   */
  async init() {
    if (this.isActive) {
      CommonUtils.log('warn', this.moduleName, 'Module already active');
      return;
    }

    try {
      CommonUtils.log('info', this.moduleName, 'Initializing module');

      // 检查URL匹配
      if (!this.isMatch()) {
        throw new Error('URL does not match this module');
      }

      // 等待页面关键元素加载
      await this.waitForPageReady();

      // 初始化功能
      await this.initializeFeatures();

      this.isActive = true;
      CommonUtils.log('info', this.moduleName, 'Module initialized successfully');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Module initialization failed', error);
      throw error;
    }
  }

  /**
   * 等待页面准备就绪
   */
  async waitForPageReady() {
    CommonUtils.log('info', this.moduleName, 'Waiting for page to be ready');

    try {
      // 等待页面基本结构加载
      // 由于这是API页面，可能需要等待JSON数据渲染或特定元素
      await CommonUtils.waitForElement('body', 5000);
      
      // 额外等待一段时间确保动态内容加载完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      CommonUtils.log('info', this.moduleName, 'Page ready detected');

    } catch (error) {
      CommonUtils.log('warn', this.moduleName, 'Page ready detection timeout, proceeding anyway');
    }
  }

  /**
   * 初始化功能特性
   */
  async initializeFeatures() {
    CommonUtils.log('info', this.moduleName, 'Initializing features');

    // 功能1：设置点击跳转功能（具体实现在第二阶段）
    this.setupClickJumpFeature();

    // 功能2：设置页面监听器
    this.setupPageObservers();

    // 功能3：设置事件监听器
    this.setupEventListeners();

    CommonUtils.log('info', this.moduleName, 'Features initialized');
  }

  /**
   * 设置点击跳转功能
   * 第二阶段将实现具体的跳转逻辑
   */
  setupClickJumpFeature() {
    CommonUtils.log('info', this.moduleName, 'Setting up click jump feature');
    
    // TODO: 第二阶段实现
    // - 识别可点击的热榜项目
    // - 添加点击事件监听器
    // - 实现跳转逻辑
    
    CommonUtils.log('info', this.moduleName, 'Click jump feature setup completed (placeholder)');
  }

  /**
   * 设置页面观察器
   */
  setupPageObservers() {
    CommonUtils.log('info', this.moduleName, 'Setting up page observers');

    // 监听DOM变化，处理动态加载的内容
    const observer = new MutationObserver(CommonUtils.throttle((mutations) => {
      this.handlePageChanges(mutations);
    }, 500));

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false
    });

    this.observers.push(observer);
    CommonUtils.log('info', this.moduleName, 'Page observers setup completed');
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    CommonUtils.log('info', this.moduleName, 'Setting up event listeners');

    // 页面点击事件监听器（用于跳转功能）
    const clickHandler = (event) => {
      this.handlePageClick(event);
    };

    document.addEventListener('click', clickHandler);
    this.eventListeners.push({
      element: document,
      event: 'click',
      handler: clickHandler
    });

    CommonUtils.log('info', this.moduleName, 'Event listeners setup completed');
  }

  /**
   * 处理页面变化
   * @param {MutationRecord[]} mutations - DOM变化记录
   */
  handlePageChanges(mutations) {
    // 检查是否有新的内容添加
    const hasNewContent = mutations.some(mutation => 
      mutation.addedNodes.length > 0
    );

    if (hasNewContent) {
      CommonUtils.log('info', this.moduleName, 'New content detected, refreshing features');
      // 重新初始化相关功能
      this.setupClickJumpFeature();
    }
  }

  /**
   * 处理页面点击事件
   * @param {Event} event - 点击事件
   */
  handlePageClick(event) {
    // TODO: 第二阶段实现具体的点击处理逻辑
    // 这里只是占位符，用于演示事件处理结构
    
    const target = event.target;
    CommonUtils.log('info', this.moduleName, 'Click detected', {
      tagName: target.tagName,
      className: target.className,
      textContent: target.textContent?.substring(0, 50)
    });
  }

  /**
   * 销毁模块
   */
  async destroy() {
    if (!this.isActive) {
      CommonUtils.log('warn', this.moduleName, 'Module not active, nothing to destroy');
      return;
    }

    try {
      CommonUtils.log('info', this.moduleName, 'Destroying module');

      // 清理观察器
      this.observers.forEach(observer => observer.disconnect());
      this.observers = [];

      // 清理事件监听器
      this.eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      this.eventListeners = [];

      // 清理添加的DOM元素
      this.cleanupDOMElements();

      this.isActive = false;
      CommonUtils.log('info', this.moduleName, 'Module destroyed successfully');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Module destruction failed', error);
    }
  }

  /**
   * 清理DOM元素
   */
  cleanupDOMElements() {
    // 移除所有由此模块添加的DOM元素
    const elementsToRemove = document.querySelectorAll('[data-money-extension="cls-rank"]');
    elementsToRemove.forEach(element => element.remove());
    
    CommonUtils.log('info', this.moduleName, `Cleaned up ${elementsToRemove.length} DOM elements`);
  }

  /**
   * 获取模块状态信息
   * @returns {Object} 模块状态
   */
  getStatus() {
    return {
      moduleName: this.moduleName,
      isActive: this.isActive,
      urlPatterns: this.urlPatterns,
      observersCount: this.observers.length,
      eventListenersCount: this.eventListeners.length,
      currentUrl: window.location.href,
      isMatch: this.isMatch()
    };
  }
}

// 将模块类暴露到全局作用域，供主入口文件使用
window.ClsRankModule = ClsRankModule;
