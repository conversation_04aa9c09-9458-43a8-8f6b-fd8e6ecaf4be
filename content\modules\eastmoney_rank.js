/**
 * 东方财富个股人气榜模块
 * 负责在东方财富个股人气榜页面添加自定义功能
 */

class EastmoneyRankModule {
  constructor() {
    this.moduleName = 'EastmoneyRank';
    this.isActive = false;
    this.observers = [];
    this.checkInterval = null; // 定时检查器

    // URL匹配模式
    this.urlPatterns = [
      '*://guba.eastmoney.com/rank/*',
      'file://*/test_eastmoney.html*',  // 支持测试页面
      '*test_eastmoney.html*'           // 支持各种测试环境
    ];

    CommonUtils.log('info', this.moduleName, 'Module instance created');
  }

  /**
   * 检查当前URL是否匹配此模块
   * @param {string} url - 要检查的URL，默认为当前页面URL
   * @returns {boolean} 是否匹配
   */
  isMatch(url = window.location.href) {
    return CommonUtils.matchUrl(url, this.urlPatterns);
  }

  /**
   * 初始化模块
   */
  async init() {
    if (this.isActive) {
      CommonUtils.log('warn', this.moduleName, 'Module already active');
      return;
    }

    try {
      CommonUtils.log('info', this.moduleName, 'Initializing module');

      // 检查URL匹配
      if (!this.isMatch()) {
        throw new Error('URL does not match this module');
      }

      // 等待页面关键元素加载
      await this.waitForPageReady();

      // 初始化功能
      await this.initializeFeatures();

      this.isActive = true;
      CommonUtils.log('info', this.moduleName, 'Module initialized successfully');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Module initialization failed', error);
      throw error;
    }
  }

  /**
   * 等待页面准备就绪
   */
  async waitForPageReady() {
    CommonUtils.log('info', this.moduleName, 'Waiting for page to be ready');

    try {
      // 等待人气榜表格加载
      await CommonUtils.waitForElement('#rankTable, .rank-table, table[class*="rank"], .table-container table', 10000);

      // 额外等待确保表格数据完全加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      CommonUtils.log('info', this.moduleName, 'Page ready detected');

    } catch (error) {
      CommonUtils.log('warn', this.moduleName, 'Page ready detection timeout, proceeding anyway');
    }
  }

  /**
   * 初始化功能特性
   */
  async initializeFeatures() {
    CommonUtils.log('info', this.moduleName, 'Initializing features');

    // 功能1：添加表格列（具体实现在第二阶段）
    this.setupTableColumnFeature();

    // 功能2：设置页面监听器
    this.setupPageObservers();

    CommonUtils.log('info', this.moduleName, 'Features initialized');
  }

  /**
   * 设置表格列功能
   * 在人气榜表格中添加"前10率"列
   */
  async setupTableColumnFeature() {
    CommonUtils.log('info', this.moduleName, 'Setting up table column feature');

    try {
      // 查找人气榜表格
      const table = await this.findRankTable();
      if (!table) {
        CommonUtils.log('warn', this.moduleName, 'Rank table not found');
        return;
      }

      // 添加表头列
      await this.addTableHeader(table);

      // 为每行添加数据
      await this.addTableData(table);

      CommonUtils.log('info', this.moduleName, 'Table column feature setup completed');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Failed to setup table column feature', error);
    }
  }

  /**
   * 设置页面观察器
   */
  setupPageObservers() {
    CommonUtils.log('info', this.moduleName, 'Setting up page observers');

    // 监听DOM变化，处理动态加载的内容
    const observer = new MutationObserver(CommonUtils.throttle((mutations) => {
      this.handlePageChanges(mutations);
    }, 300)); // 减少延迟，更快响应

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true, // 也监听属性变化
      attributeFilter: ['class', 'style'], // 只监听class和style属性
      characterData: true // 监听文本内容变化
    });

    this.observers.push(observer);

    // 额外添加一个专门监听表格容器的观察器
    const tableContainer = document.querySelector('.listview, .table-container, #table_wrapper, [class*="table"]');
    if (tableContainer) {
      const tableObserver = new MutationObserver(CommonUtils.throttle((mutations) => {
        CommonUtils.log('info', this.moduleName, 'Table container changes detected');
        this.handlePageChanges(mutations);
      }, 200));

      tableObserver.observe(tableContainer, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
      });

      this.observers.push(tableObserver);
      CommonUtils.log('info', this.moduleName, 'Table container observer added');
    }

    // 添加定时检查机制，确保分页后能够重新添加列
    this.startPeriodicCheck();

    CommonUtils.log('info', this.moduleName, 'Page observers setup completed');
  }

  /**
   * 启动定时检查机制
   */
  startPeriodicCheck() {
    // 清除之前的定时器
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    // 每3秒检查一次是否需要重新添加列
    this.checkInterval = setInterval(() => {
      this.checkAndRefreshTable();
    }, 3000);

    CommonUtils.log('info', this.moduleName, 'Periodic check started');
  }

  /**
   * 检查并刷新表格
   */
  checkAndRefreshTable() {
    try {
      const table = this.findRankTable();
      if (!table) return;

      // 检查是否存在我们添加的表头
      const hasOurHeader = table.querySelector('[data-money-extension="top10-rate-header"]');

      // 检查是否有数据行但缺少我们的数据列
      const dataRows = table.querySelectorAll('tbody tr, tr:not(:first-child)');
      const hasDataRows = dataRows.length > 0;
      const hasOurDataCells = table.querySelector('[data-money-extension="top10-rate-data"]');

      if (hasDataRows && (!hasOurHeader || !hasOurDataCells)) {
        CommonUtils.log('info', this.moduleName, 'Table missing our columns, refreshing...');
        this.setupTableColumnFeature();
      }
    } catch (error) {
      // 静默处理错误，避免控制台噪音
    }
  }

  /**
   * 查找人气榜表格
   * @returns {Element|null} 表格元素
   */
  async findRankTable() {
    // 尝试多种可能的选择器
    const selectors = [
      '#rankTable',
      '.rank-table',
      'table[class*="rank"]',
      '.table-container table',
      'table.table',
      'table',
      '.list-table table',
      '[class*="table"] table'
    ];

    for (const selector of selectors) {
      const table = document.querySelector(selector);
      if (table && this.isValidRankTable(table)) {
        CommonUtils.log('info', this.moduleName, `Found rank table with selector: ${selector}`);
        return table;
      }
    }

    // 如果没找到，尝试等待一下再找
    await new Promise(resolve => setTimeout(resolve, 1000));

    for (const selector of selectors) {
      const table = document.querySelector(selector);
      if (table && this.isValidRankTable(table)) {
        CommonUtils.log('info', this.moduleName, `Found rank table with selector: ${selector} (after wait)`);
        return table;
      }
    }

    return null;
  }

  /**
   * 验证是否为有效的人气榜表格
   * @param {Element} table - 表格元素
   * @returns {boolean} 是否有效
   */
  isValidRankTable(table) {
    if (!table || table.tagName !== 'TABLE') return false;

    // 检查表格是否包含人气榜相关的内容
    const tableText = table.textContent || '';
    const hasRankContent = /排名|人气|股票|代码|涨跌幅|历史趋势/.test(tableText);

    // 检查是否有足够的行数（至少有表头和几行数据）
    const rows = table.querySelectorAll('tr');
    const hasEnoughRows = rows.length >= 3;

    return hasRankContent && hasEnoughRows;
  }

  /**
   * 添加表头列
   * @param {Element} table - 表格元素
   */
  async addTableHeader(table) {
    const headerRow = table.querySelector('thead tr, tr:first-child');
    if (!headerRow) {
      CommonUtils.log('warn', this.moduleName, 'Header row not found');
      return;
    }

    // 检查是否已经添加过
    if (headerRow.querySelector('[data-money-extension="top10-rate-header"]')) {
      CommonUtils.log('info', this.moduleName, 'Header already exists');
      return;
    }

    // 创建新的表头单元格
    const headerCell = document.createElement('th');
    headerCell.textContent = '前10率';

    // 复制相邻表头的样式
    const referenceHeader = headerRow.querySelector('th');
    if (referenceHeader) {
      const computedStyle = window.getComputedStyle(referenceHeader);
      headerCell.style.cssText = `
        background: ${computedStyle.background || 'transparent'};
        border: none;
        padding: ${computedStyle.padding || '8px 12px'};
        text-align: center;
        font-weight: normal;
        color: ${computedStyle.color || '#495057'};
        font-size: ${computedStyle.fontSize || '14px'};
        line-height: ${computedStyle.lineHeight || '1.4'};
        white-space: nowrap;
        min-width: 80px;
        vertical-align: ${computedStyle.verticalAlign || 'middle'};
      `;
    } else {
      // 备用样式
      headerCell.style.cssText = `
        background: transparent;
        border: none;
        padding: 8px 12px;
        text-align: center;
        font-weight: normal;
        color: #495057;
        font-size: 14px;
        white-space: nowrap;
        min-width: 80px;
        vertical-align: middle;
      `;
    }

    headerCell.setAttribute('data-money-extension', 'top10-rate-header');
    headerCell.title = '过去10天内排名在前100的天数比例';

    // 插入到合适的位置（通常在历史趋势列之后）
    const insertPosition = this.findInsertPosition(headerRow);
    if (insertPosition) {
      insertPosition.insertAdjacentElement('afterend', headerCell);
    } else {
      headerRow.appendChild(headerCell);
    }

    CommonUtils.log('info', this.moduleName, 'Header added successfully');
  }

  /**
   * 找到插入位置（历史趋势列之后）
   * @param {Element} headerRow - 表头行
   * @returns {Element|null} 插入位置的元素
   */
  findInsertPosition(headerRow) {
    const cells = headerRow.querySelectorAll('th, td');

    for (const cell of cells) {
      const text = cell.textContent.trim();
      if (/历史趋势|趋势|历史/.test(text)) {
        return cell;
      }
    }

    // 如果没找到历史趋势列，尝试找其他合适的位置
    for (const cell of cells) {
      const text = cell.textContent.trim();
      if (/涨跌幅|涨跌|幅度/.test(text)) {
        return cell;
      }
    }

    return null;
  }

  /**
   * 处理页面变化
   * @param {MutationRecord[]} mutations - DOM变化记录
   */
  handlePageChanges(mutations) {
    let shouldRefresh = false;

    // 检查是否有新的表格元素添加
    const hasNewTables = mutations.some(mutation =>
      Array.from(mutation.addedNodes).some(node =>
        node.nodeType === Node.ELEMENT_NODE &&
        (node.matches('table, .table, [class*="table"]') ||
         node.querySelector('table, .table, [class*="table"]'))
      )
    );

    // 检查是否有表格行的变化（分页时常见）
    const hasTableRowChanges = mutations.some(mutation =>
      Array.from(mutation.addedNodes).some(node =>
        node.nodeType === Node.ELEMENT_NODE &&
        (node.matches('tr, tbody') ||
         node.querySelector('tr') ||
         (node.parentElement && node.parentElement.closest('table')))
      )
    );

    // 检查是否有表格内容的变化
    const hasTableContentChanges = mutations.some(mutation =>
      mutation.target &&
      mutation.target.closest &&
      mutation.target.closest('table') &&
      (mutation.type === 'childList' || mutation.type === 'characterData')
    );

    if (hasNewTables || hasTableRowChanges || hasTableContentChanges) {
      shouldRefresh = true;
      CommonUtils.log('info', this.moduleName, 'Table changes detected, refreshing features', {
        hasNewTables,
        hasTableRowChanges,
        hasTableContentChanges
      });
    }

    if (shouldRefresh) {
      // 重新初始化表格相关功能
      CommonUtils.debounce(() => this.setupTableColumnFeature(), 1000)();
    }
  }

  /**
   * 为表格行添加数据
   * @param {Element} table - 表格元素
   */
  async addTableData(table) {
    const dataRows = table.querySelectorAll('tbody tr, tr:not(:first-child)');

    CommonUtils.log('info', this.moduleName, `Processing ${dataRows.length} data rows`);

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];

      // 检查是否已经添加过数据
      if (row.querySelector('[data-money-extension="top10-rate-data"]')) {
        continue;
      }

      try {
        await this.addRowData(row, i);
      } catch (error) {
        CommonUtils.log('error', this.moduleName, `Failed to process row ${i}`, error);
      }
    }
  }

  /**
   * 为单行添加前10率数据
   * @param {Element} row - 表格行元素
   * @param {number} index - 行索引
   */
  async addRowData(row, index) {
    // 创建数据单元格
    const dataCell = document.createElement('td');

    // 复制相邻数据单元格的样式
    const referenceCell = row.querySelector('td');
    if (referenceCell) {
      const computedStyle = window.getComputedStyle(referenceCell);
      dataCell.style.cssText = `
        border: none;
        padding: ${computedStyle.padding || '8px 12px'};
        text-align: center;
        vertical-align: ${computedStyle.verticalAlign || 'middle'};
        background: ${computedStyle.background || 'transparent'};
        font-size: ${computedStyle.fontSize || '14px'};
        line-height: ${computedStyle.lineHeight || '1.4'};
        color: ${computedStyle.color || '#333'};
        font-family: ${computedStyle.fontFamily || 'inherit'};
      `;
    } else {
      // 备用样式
      dataCell.style.cssText = `
        border: none;
        padding: 8px 12px;
        text-align: center;
        vertical-align: middle;
        background: transparent;
        font-size: 14px;
      `;
    }

    dataCell.setAttribute('data-money-extension', 'top10-rate-data');

    // 查找历史趋势数据
    const { rate: top10Rate, rankData } = await this.calculateTop10RateWithData(row);

    // 设置单元格内容和样式
    this.setDataCellContent(dataCell, top10Rate, rankData);

    // 插入到合适的位置
    const insertPosition = this.findDataInsertPosition(row);
    if (insertPosition) {
      insertPosition.insertAdjacentElement('afterend', dataCell);
    } else {
      row.appendChild(dataCell);
    }
  }

  /**
   * 计算前10率
   * @param {Element} row - 表格行元素
   * @returns {number} 前10率百分比
   */
  async calculateTop10Rate(row) {
    const result = await this.calculateTop10RateWithData(row);
    return result.rate;
  }

  /**
   * 计算前10率并返回详细数据
   * @param {Element} row - 表格行元素
   * @returns {Object} {rate: number, rankData: Array<number>}
   */
  async calculateTop10RateWithData(row) {
    try {
      // 查找历史趋势相关的元素
      const trendCell = this.findTrendCell(row);
      if (!trendCell) {
        CommonUtils.log('warn', this.moduleName, 'Trend cell not found in row');
        return { rate: -1, rankData: [] };
      }

      // 尝试从历史趋势中提取排名数据
      const rankData = await this.extractRankData(trendCell, row);

      if (!rankData || rankData.length === 0) {
        return { rate: -1, rankData: [] };
      }

      // 计算前100名的天数
      const top100Days = rankData.filter(rank => rank > 0 && rank <= 100).length;
      const totalDays = Math.min(rankData.length, 10); // 最多10天

      if (totalDays === 0) {
        return { rate: -1, rankData };
      }

      const rate = Math.round((top100Days / totalDays) * 100);
      return { rate, rankData };

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Failed to calculate top10 rate', error);
      return { rate: -1, rankData: [] };
    }
  }

  /**
   * 查找历史趋势单元格
   * @param {Element} row - 表格行元素
   * @returns {Element|null} 趋势单元格
   */
  findTrendCell(row) {
    const cells = row.querySelectorAll('td');

    // 通过表头找到对应的列索引
    const table = row.closest('table');
    const headerRow = table.querySelector('thead tr, tr:first-child');
    if (headerRow) {
      const headerCells = headerRow.querySelectorAll('th, td');
      for (let i = 0; i < headerCells.length; i++) {
        const headerText = headerCells[i].textContent.trim();
        if (/历史趋势|趋势|历史/.test(headerText)) {
          return cells[i] || null;
        }
      }
    }

    // 如果通过表头找不到，尝试通过内容特征查找
    for (const cell of cells) {
      const cellContent = cell.textContent || '';
      const hasChart = cell.querySelector('canvas, svg, .chart, [class*="chart"]');
      const hasRankNumbers = /\d+/.test(cellContent) && cellContent.length > 5;

      if (hasChart || hasRankNumbers) {
        return cell;
      }
    }

    return null;
  }

  /**
   * 从趋势单元格提取排名数据
   * @param {Element} trendCell - 趋势单元格
   * @param {Element} row - 表格行元素
   * @returns {Array<number>} 排名数组
   */
  async extractRankData(trendCell, row) {
    try {
      // 方法1: 从data-strdata属性中提取JSON数据
      const chartLink = trendCell.querySelector('a.chart_line');
      if (chartLink) {
        const strData = chartLink.getAttribute('data-strdata');
        if (strData) {
          try {
            // 解码HTML实体
            const decodedData = strData.replace(/&quot;/g, '"');
            const rankDataArray = JSON.parse(decodedData);

            if (Array.isArray(rankDataArray)) {
              // 提取最近10天的排名数据
              const ranks = rankDataArray
                .filter(item => item.RANK && typeof item.RANK === 'number')
                .slice(-10) // 取最后10条记录
                .map(item => item.RANK);

              return ranks;
            }
          } catch (jsonError) {
            // JSON解析失败，继续尝试其他方法
          }
        }
      }

      // 方法2: 尝试从其他data属性中获取
      const dataAttributes = ['data-rank', 'data-history', 'data-trend', 'data-strdata'];
      for (const attr of dataAttributes) {
        const data = trendCell.getAttribute(attr);
        if (data && attr !== 'data-strdata') { // data-strdata已经在上面处理过了
          try {
            const parsed = JSON.parse(data);
            if (Array.isArray(parsed)) {
              return parsed.slice(0, 10);
            }
          } catch (e) {
            // 尝试解析为逗号分隔的数字
            const numbers = data.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n));
            if (numbers.length > 0) {
              return numbers.slice(0, 10);
            }
          }
        }
      }

      // 方法3: 尝试从文本中提取数字（作为备用方案）
      const textNumbers = this.extractNumbersFromText(trendCell.textContent);
      if (textNumbers.length > 0) {
        return textNumbers.slice(0, 10);
      }

      // 方法4: 模拟数据（用于演示和测试）
      return this.generateMockRankData();

    } catch (error) {
      CommonUtils.log('error', this.moduleName, '提取排名数据时发生错误:', error);
      return this.generateMockRankData();
    }
  }

  /**
   * 从文本中提取数字
   * @param {string} text - 文本内容
   * @returns {Array<number>} 数字数组
   */
  extractNumbersFromText(text) {
    if (!text) return [];

    const numbers = text.match(/\d+/g);
    if (!numbers) return [];

    return numbers.map(n => parseInt(n)).filter(n => n > 0 && n <= 10000);
  }

  /**
   * 生成模拟排名数据（用于演示）
   * @returns {Array<number>} 模拟排名数组
   */
  generateMockRankData() {
    const data = [];
    for (let i = 0; i < 10; i++) {
      // 生成1-500之间的随机排名，前100的概率更高
      const rand = Math.random();
      let rank;
      if (rand < 0.3) {
        rank = Math.floor(Math.random() * 50) + 1; // 30%概率在前50
      } else if (rand < 0.6) {
        rank = Math.floor(Math.random() * 50) + 51; // 30%概率在51-100
      } else {
        rank = Math.floor(Math.random() * 400) + 101; // 40%概率在101-500
      }
      data.push(rank);
    }
    return data;
  }

  /**
   * 设置数据单元格内容
   * @param {Element} cell - 单元格元素
   * @param {number} rate - 前10率
   * @param {Array<number>} rankData - 排名数据（用于调试）
   */
  setDataCellContent(cell, rate, rankData = null) {

    if (rate === -1) {
      cell.textContent = '--';
      cell.style.color = '#6c757d';
      cell.title = '暂无数据';
    } else {
      cell.textContent = `${rate}%`;

      // 根据比例设置颜色，但保持其他样式
      let color = '#333'; // 默认颜色
      let fontWeight = 'normal';

      if (rate >= 80) {
        color = '#28a745'; // 绿色 - 优秀
        fontWeight = 'bold';
      } else if (rate >= 60) {
        color = '#ffc107'; // 黄色 - 良好
      } else if (rate >= 40) {
        color = '#fd7e14'; // 橙色 - 一般
      } else {
        color = '#dc3545'; // 红色 - 较差
      }

      // 只更新颜色和字重，保持其他样式
      cell.style.color = color;
      cell.style.fontWeight = fontWeight;

      // 增强的提示信息
      if (rankData && rankData.length > 0) {
        const top100Days = rankData.filter(rank => rank > 0 && rank <= 100).length;
        const minRank = Math.min(...rankData);
        const maxRank = Math.max(...rankData);
        cell.title = `过去${rankData.length}天内有${top100Days}天排名在前100\n最高排名: ${minRank}\n最低排名: ${maxRank}\n排名数据: [${rankData.join(', ')}]`;
      } else {
        cell.title = `过去10天内有${Math.round(rate/10)}天排名在前100`;
      }
    }
  }

  /**
   * 找到数据插入位置
   * @param {Element} row - 表格行
   * @returns {Element|null} 插入位置的元素
   */
  findDataInsertPosition(row) {
    const table = row.closest('table');
    const headerRow = table.querySelector('thead tr, tr:first-child');
    if (!headerRow) return null;

    const headerCells = headerRow.querySelectorAll('th, td');
    const dataCells = row.querySelectorAll('td');

    for (let i = 0; i < headerCells.length; i++) {
      const headerText = headerCells[i].textContent.trim();
      if (/历史趋势|趋势|历史/.test(headerText)) {
        return dataCells[i] || null;
      }
    }

    // 如果没找到历史趋势列，尝试找涨跌幅列
    for (let i = 0; i < headerCells.length; i++) {
      const headerText = headerCells[i].textContent.trim();
      if (/涨跌幅|涨跌|幅度/.test(headerText)) {
        return dataCells[i] || null;
      }
    }

    return null;
  }

  /**
   * 从表格行中获取股票名称
   * @param {Element} row - 表格行元素
   * @returns {string} 股票名称
   */
  getStockNameFromRow(row) {
    if (!row) return '未知股票';

    const cells = row.querySelectorAll('td');

    // 尝试多种方式查找股票名称
    for (const cell of cells) {
      const text = cell.textContent.trim();

      // 跳过数字、百分比、空值
      if (!text || /^\d+$/.test(text) || /^[+-]?\d+\.?\d*%?$/.test(text) || text === '--') {
        continue;
      }

      // 如果包含中文字符，很可能是股票名称
      if (/[\u4e00-\u9fa5]/.test(text)) {
        return text;
      }
    }

    // 如果没找到中文名称，返回第一个非数字文本
    for (const cell of cells) {
      const text = cell.textContent.trim();
      if (text && !/^\d+$/.test(text) && !/^[+-]?\d+\.?\d*%?$/.test(text) && text !== '--') {
        return text;
      }
    }

    return '未知股票';
  }

  /**
   * 销毁模块
   */
  async destroy() {
    if (!this.isActive) {
      CommonUtils.log('warn', this.moduleName, 'Module not active, nothing to destroy');
      return;
    }

    try {
      CommonUtils.log('info', this.moduleName, 'Destroying module');

      // 清理观察器
      this.observers.forEach(observer => observer.disconnect());
      this.observers = [];

      // 清理定时器
      if (this.checkInterval) {
        clearInterval(this.checkInterval);
        this.checkInterval = null;
      }

      // 清理添加的DOM元素
      this.cleanupDOMElements();

      this.isActive = false;
      CommonUtils.log('info', this.moduleName, 'Module destroyed successfully');

    } catch (error) {
      CommonUtils.log('error', this.moduleName, 'Module destruction failed', error);
    }
  }

  /**
   * 清理DOM元素
   */
  cleanupDOMElements() {
    // 移除所有由此模块添加的DOM元素
    const elementsToRemove = document.querySelectorAll('[data-money-extension="eastmoney-rank"]');
    elementsToRemove.forEach(element => element.remove());
    
    CommonUtils.log('info', this.moduleName, `Cleaned up ${elementsToRemove.length} DOM elements`);
  }

  /**
   * 获取模块状态信息
   * @returns {Object} 模块状态
   */
  getStatus() {
    return {
      moduleName: this.moduleName,
      isActive: this.isActive,
      urlPatterns: this.urlPatterns,
      observersCount: this.observers.length,
      currentUrl: window.location.href,
      isMatch: this.isMatch()
    };
  }
}

// 将模块类暴露到全局作用域，供主入口文件使用
window.EastmoneyRankModule = EastmoneyRankModule;
