<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排名数据调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-case {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .test-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .test-data {
            font-family: monospace;
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .test-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            color: #155724;
        }
        
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 排名数据调试测试</h1>
        
        <p>这个页面用于测试和调试排名数据的提取和计算逻辑。</p>
        
        <button class="button" onclick="runAllTests()">运行所有测试</button>
        <button class="button" onclick="clearResults()">清空结果</button>
        
        <div id="testResults"></div>
    </div>

    <script>
        // 测试数据集 - 使用真实的JSON格式
        const testCases = [
            {
                name: "贵州茅台",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":15},{"CALCTIME":"2025-05-23 23:50:00","RANK":23},{"CALCTIME":"2025-05-24 23:50:00","RANK":8},{"CALCTIME":"2025-05-25 23:50:00","RANK":45},{"CALCTIME":"2025-05-26 23:50:00","RANK":67},{"CALCTIME":"2025-05-27 23:50:00","RANK":12},{"CALCTIME":"2025-05-28 23:50:00","RANK":89},{"CALCTIME":"2025-05-29 23:50:00","RANK":34},{"CALCTIME":"2025-05-30 23:50:00","RANK":56},{"CALCTIME":"2025-05-31 23:50:00","RANK":78}]`,
                description: "包含多个前100排名的数据"
            },
            {
                name: "中国平安",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":458},{"CALCTIME":"2025-05-23 23:50:00","RANK":447},{"CALCTIME":"2025-05-24 23:50:00","RANK":426},{"CALCTIME":"2025-05-25 23:50:00","RANK":421},{"CALCTIME":"2025-05-26 23:50:00","RANK":430},{"CALCTIME":"2025-05-27 23:50:00","RANK":324},{"CALCTIME":"2025-05-28 23:50:00","RANK":589},{"CALCTIME":"2025-05-29 23:50:00","RANK":197},{"CALCTIME":"2025-05-30 23:50:00","RANK":20},{"CALCTIME":"2025-05-31 23:50:00","RANK":15}]`,
                description: "混合前100和后100的排名"
            },
            {
                name: "招商银行",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":78},{"CALCTIME":"2025-05-23 23:50:00","RANK":45},{"CALCTIME":"2025-05-24 23:50:00","RANK":123},{"CALCTIME":"2025-05-25 23:50:00","RANK":67},{"CALCTIME":"2025-05-26 23:50:00","RANK":89},{"CALCTIME":"2025-05-27 23:50:00","RANK":34},{"CALCTIME":"2025-05-28 23:50:00","RANK":156},{"CALCTIME":"2025-05-29 23:50:00","RANK":23},{"CALCTIME":"2025-05-30 23:50:00","RANK":78},{"CALCTIME":"2025-05-31 23:50:00","RANK":45}]`,
                description: "大部分在前100的排名"
            },
            {
                name: "腾讯控股",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":156},{"CALCTIME":"2025-05-23 23:50:00","RANK":234},{"CALCTIME":"2025-05-24 23:50:00","RANK":78},{"CALCTIME":"2025-05-25 23:50:00","RANK":45},{"CALCTIME":"2025-05-26 23:50:00","RANK":123},{"CALCTIME":"2025-05-27 23:50:00","RANK":67},{"CALCTIME":"2025-05-28 23:50:00","RANK":89},{"CALCTIME":"2025-05-29 23:50:00","RANK":234},{"CALCTIME":"2025-05-30 23:50:00","RANK":156},{"CALCTIME":"2025-05-31 23:50:00","RANK":78}]`,
                description: "部分超过100的排名"
            },
            {
                name: "全部前10",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":1},{"CALCTIME":"2025-05-23 23:50:00","RANK":2},{"CALCTIME":"2025-05-24 23:50:00","RANK":3},{"CALCTIME":"2025-05-25 23:50:00","RANK":4},{"CALCTIME":"2025-05-26 23:50:00","RANK":5},{"CALCTIME":"2025-05-27 23:50:00","RANK":6},{"CALCTIME":"2025-05-28 23:50:00","RANK":7},{"CALCTIME":"2025-05-29 23:50:00","RANK":8},{"CALCTIME":"2025-05-30 23:50:00","RANK":9},{"CALCTIME":"2025-05-31 23:50:00","RANK":10}]`,
                description: "全部都是前10的排名"
            },
            {
                name: "全部前50",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":11},{"CALCTIME":"2025-05-23 23:50:00","RANK":22},{"CALCTIME":"2025-05-24 23:50:00","RANK":33},{"CALCTIME":"2025-05-25 23:50:00","RANK":44},{"CALCTIME":"2025-05-26 23:50:00","RANK":15},{"CALCTIME":"2025-05-27 23:50:00","RANK":26},{"CALCTIME":"2025-05-28 23:50:00","RANK":37},{"CALCTIME":"2025-05-29 23:50:00","RANK":48},{"CALCTIME":"2025-05-30 23:50:00","RANK":19},{"CALCTIME":"2025-05-31 23:50:00","RANK":30}]`,
                description: "全部都是前50的排名"
            },
            {
                name: "全部前100",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":51},{"CALCTIME":"2025-05-23 23:50:00","RANK":62},{"CALCTIME":"2025-05-24 23:50:00","RANK":73},{"CALCTIME":"2025-05-25 23:50:00","RANK":84},{"CALCTIME":"2025-05-26 23:50:00","RANK":95},{"CALCTIME":"2025-05-27 23:50:00","RANK":56},{"CALCTIME":"2025-05-28 23:50:00","RANK":67},{"CALCTIME":"2025-05-29 23:50:00","RANK":78},{"CALCTIME":"2025-05-30 23:50:00","RANK":89},{"CALCTIME":"2025-05-31 23:50:00","RANK":90}]`,
                description: "全部都是前100的排名"
            },
            {
                name: "全部后100",
                jsonData: `[{"CALCTIME":"2025-05-22 23:50:00","RANK":156},{"CALCTIME":"2025-05-23 23:50:00","RANK":234},{"CALCTIME":"2025-05-24 23:50:00","RANK":345},{"CALCTIME":"2025-05-25 23:50:00","RANK":456},{"CALCTIME":"2025-05-26 23:50:00","RANK":567},{"CALCTIME":"2025-05-27 23:50:00","RANK":678},{"CALCTIME":"2025-05-28 23:50:00","RANK":789},{"CALCTIME":"2025-05-29 23:50:00","RANK":890},{"CALCTIME":"2025-05-30 23:50:00","RANK":123},{"CALCTIME":"2025-05-31 23:50:00","RANK":234}]`,
                description: "全部都是100名以后的排名"
            },
            {
                name: "空数据",
                jsonData: "",
                description: "空的趋势数据"
            },
            {
                name: "无效JSON",
                jsonData: "abc,def,ghi",
                description: "包含无效JSON的数据"
            }
        ];

        // 从JSON数据中提取排名数据
        function extractRankData(jsonData) {
            if (!jsonData) return [];

            try {
                // 尝试解析JSON
                const rankDataArray = JSON.parse(jsonData);

                if (Array.isArray(rankDataArray)) {
                    // 提取最近10天的排名数据
                    const ranks = rankDataArray
                        .filter(item => item.RANK && typeof item.RANK === 'number')
                        .slice(-10) // 取最后10条记录
                        .map(item => item.RANK);

                    return ranks;
                }
            } catch (error) {
                console.log('JSON解析失败，尝试从文本中提取数字:', error);

                // 备用方案：从文本中提取数字
                const numbers = jsonData.match(/\d+/g);
                if (numbers) {
                    return numbers.map(n => parseInt(n)).filter(n => n > 0 && n <= 10000).slice(0, 10);
                }
            }

            return [];
        }

        // 计算前10率
        function calculateTop10Rate(rankData) {
            if (!rankData || rankData.length === 0) return -1;
            
            const top100Days = rankData.filter(rank => rank > 0 && rank <= 100).length;
            const totalDays = Math.min(rankData.length, 10);
            
            if (totalDays === 0) return -1;
            
            return Math.round((top100Days / totalDays) * 100);
        }

        // 分析排名数据
        function analyzeRankData(rankData) {
            if (!rankData || rankData.length === 0) {
                return {
                    totalDays: 0,
                    top10Days: 0,
                    top50Days: 0,
                    top100Days: 0,
                    minRank: null,
                    maxRank: null,
                    avgRank: null
                };
            }

            const top10Days = rankData.filter(rank => rank > 0 && rank <= 10);
            const top50Days = rankData.filter(rank => rank > 0 && rank <= 50);
            const top100Days = rankData.filter(rank => rank > 0 && rank <= 100);

            return {
                totalDays: rankData.length,
                top10Days: top10Days.length,
                top50Days: top50Days.length,
                top100Days: top100Days.length,
                minRank: Math.min(...rankData),
                maxRank: Math.max(...rankData),
                avgRank: Math.round(rankData.reduce((a, b) => a + b, 0) / rankData.length)
            };
        }

        // 运行单个测试
        function runTest(testCase) {
            console.log(`\n=== 测试: ${testCase.name} ===`);
            console.log(`原始JSON数据: ${testCase.jsonData.substring(0, 100)}...`);

            // 提取排名数据
            const rankData = extractRankData(testCase.jsonData);
            console.log(`提取的排名数据:`, rankData);

            // 计算前10率
            const top10Rate = calculateTop10Rate(rankData);
            console.log(`前10率: ${top10Rate}%`);

            // 详细分析
            const analysis = analyzeRankData(rankData);
            console.log(`详细分析:`, analysis);

            return {
                testCase,
                rankData,
                top10Rate,
                analysis
            };
        }

        // 显示测试结果
        function displayTestResult(result) {
            const container = document.getElementById('testResults');
            
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';
            
            const { testCase, rankData, top10Rate, analysis } = result;
            
            let colorClass = '';
            if (top10Rate >= 80) colorClass = 'color: #28a745; font-weight: bold;';
            else if (top10Rate >= 60) colorClass = 'color: #ffc107;';
            else if (top10Rate >= 40) colorClass = 'color: #fd7e14;';
            else if (top10Rate >= 0) colorClass = 'color: #dc3545;';
            else colorClass = 'color: #6c757d;';
            
            const jsonPreview = testCase.jsonData ? testCase.jsonData.substring(0, 80) + '...' : '无数据';

            testDiv.innerHTML = `
                <div class="test-title">${testCase.name} - ${testCase.description}</div>
                <div class="test-data">JSON数据: ${jsonPreview}</div>
                <div class="test-data">提取排名: [${rankData.join(', ')}]</div>
                <div class="test-result">
                    <strong>前10率: <span style="${colorClass}">${top10Rate === -1 ? '--' : top10Rate + '%'}</span></strong><br>
                    总天数: ${analysis.totalDays} |
                    前10名: ${analysis.top10Days}天 |
                    前50名: ${analysis.top50Days}天 |
                    前100名: ${analysis.top100Days}天<br>
                    ${analysis.minRank ? `最高排名: ${analysis.minRank} | 最低排名: ${analysis.maxRank} | 平均排名: ${analysis.avgRank}` : '无有效排名数据'}
                </div>
            `;
            
            container.appendChild(testDiv);
        }

        // 运行所有测试
        function runAllTests() {
            console.log('🚀 开始运行所有测试...');
            clearResults();
            
            testCases.forEach(testCase => {
                const result = runTest(testCase);
                displayTestResult(result);
            });
            
            console.log('✅ 所有测试完成！');
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            console.clear();
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 排名数据调试测试页面加载完成');
            console.log('点击"运行所有测试"按钮开始测试，或查看控制台输出');
        });
    </script>
</body>
</html>
