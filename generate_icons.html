<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Extension - 图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .icon-size {
            text-align: center;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .icon-size h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        canvas {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .control-group h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        label {
            display: block;
            margin: 10px 0 5px 0;
            font-weight: 500;
            color: #6c757d;
        }
        
        input, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        input[type="color"] {
            height: 40px;
            padding: 2px;
        }
        
        .download-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 8px;
        }
        
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin: 5px;
            transition: background 0.2s;
        }
        
        .download-btn:hover {
            background: #5a6fd8;
        }
        
        .info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 Money Extension 图标生成器</h1>
        
        <div class="info">
            <strong>说明：</strong>这个工具为 Money Extension Chrome插件生成所需的图标文件。
            Chrome插件需要 16x16、48x48 和 128x128 三种尺寸的图标。
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>🎨 颜色设置</h3>
                <label for="bgColor">背景颜色:</label>
                <input type="color" id="bgColor" value="#667eea">
                
                <label for="iconColor">图标颜色:</label>
                <input type="color" id="iconColor" value="#ffffff">
                
                <label for="accentColor">强调色:</label>
                <input type="color" id="accentColor" value="#ffd700">
            </div>
            
            <div class="control-group">
                <h3>📐 样式设置</h3>
                <label for="iconStyle">图标样式:</label>
                <select id="iconStyle">
                    <option value="money">💰 金钱符号</option>
                    <option value="chart">📈 图表</option>
                    <option value="coin">🪙 硬币</option>
                    <option value="trend">📊 趋势</option>
                </select>
                
                <label for="borderRadius">圆角大小:</label>
                <input type="range" id="borderRadius" min="0" max="20" value="8">
                
                <label for="shadowIntensity">阴影强度:</label>
                <input type="range" id="shadowIntensity" min="0" max="10" value="3">
            </div>
        </div>
        
        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <canvas id="icon16" width="16" height="16"></canvas>
            </div>
            <div class="icon-size">
                <h3>48x48</h3>
                <canvas id="icon48" width="48" height="48"></canvas>
            </div>
            <div class="icon-size">
                <h3>128x128</h3>
                <canvas id="icon128" width="128" height="128"></canvas>
            </div>
        </div>
        
        <div class="download-section">
            <h3>📥 下载图标</h3>
            <p>点击下面的按钮下载对应尺寸的图标文件</p>
            <button class="download-btn" onclick="downloadIcon(16)">下载 16x16</button>
            <button class="download-btn" onclick="downloadIcon(48)">下载 48x48</button>
            <button class="download-btn" onclick="downloadIcon(128)">下载 128x128</button>
            <button class="download-btn" onclick="downloadAllIcons()">下载全部</button>
        </div>
    </div>

    <script>
        // 图标绘制函数
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const bgColor = document.getElementById('bgColor').value;
            const iconColor = document.getElementById('iconColor').value;
            const accentColor = document.getElementById('accentColor').value;
            const iconStyle = document.getElementById('iconStyle').value;
            const borderRadius = parseInt(document.getElementById('borderRadius').value);
            const shadowIntensity = parseInt(document.getElementById('shadowIntensity').value);
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制阴影
            if (shadowIntensity > 0) {
                ctx.shadowColor = 'rgba(0,0,0,0.3)';
                ctx.shadowBlur = shadowIntensity;
                ctx.shadowOffsetX = 1;
                ctx.shadowOffsetY = 1;
            }
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            if (borderRadius > 0) {
                drawRoundedRect(ctx, 0, 0, size, size, borderRadius);
            } else {
                ctx.fillRect(0, 0, size, size);
            }
            
            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // 绘制图标
            ctx.fillStyle = iconColor;
            ctx.strokeStyle = iconColor;
            ctx.lineWidth = Math.max(1, size / 32);
            
            const center = size / 2;
            const iconSize = size * 0.6;
            
            switch (iconStyle) {
                case 'money':
                    drawMoneySymbol(ctx, center, center, iconSize);
                    break;
                case 'chart':
                    drawChart(ctx, center, center, iconSize);
                    break;
                case 'coin':
                    drawCoin(ctx, center, center, iconSize, accentColor);
                    break;
                case 'trend':
                    drawTrend(ctx, center, center, iconSize, accentColor);
                    break;
            }
        }
        
        // 绘制圆角矩形
        function drawRoundedRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            ctx.fill();
        }
        
        // 绘制金钱符号
        function drawMoneySymbol(ctx, x, y, size) {
            const fontSize = size * 0.8;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('¥', x, y);
        }
        
        // 绘制图表
        function drawChart(ctx, x, y, size) {
            const barWidth = size / 6;
            const barSpacing = size / 8;
            const maxHeight = size * 0.7;
            
            const bars = [0.3, 0.7, 0.5, 0.9, 0.6];
            const startX = x - (bars.length * barWidth + (bars.length - 1) * barSpacing) / 2;
            
            bars.forEach((height, i) => {
                const barX = startX + i * (barWidth + barSpacing);
                const barHeight = maxHeight * height;
                const barY = y + maxHeight / 2 - barHeight;
                
                ctx.fillRect(barX, barY, barWidth, barHeight);
            });
        }
        
        // 绘制硬币
        function drawCoin(ctx, x, y, size, accentColor) {
            const radius = size / 2.5;
            
            // 外圆
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 内圆
            ctx.fillStyle = accentColor;
            ctx.beginPath();
            ctx.arc(x, y, radius * 0.7, 0, 2 * Math.PI);
            ctx.fill();
            
            // 金钱符号
            ctx.fillStyle = ctx.strokeStyle;
            const fontSize = size * 0.4;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('¥', x, y);
        }
        
        // 绘制趋势线
        function drawTrend(ctx, x, y, size, accentColor) {
            const points = [
                [x - size/2, y + size/4],
                [x - size/4, y],
                [x, y + size/6],
                [x + size/4, y - size/4],
                [x + size/2, y - size/3]
            ];
            
            // 绘制趋势线
            ctx.beginPath();
            ctx.moveTo(points[0][0], points[0][1]);
            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i][0], points[i][1]);
            }
            ctx.stroke();
            
            // 绘制数据点
            ctx.fillStyle = accentColor;
            points.forEach(point => {
                ctx.beginPath();
                ctx.arc(point[0], point[1], size / 20, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        // 更新所有图标
        function updateIcons() {
            [16, 48, 128].forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                drawIcon(canvas, size);
            });
        }
        
        // 下载图标
        function downloadIcon(size) {
            const canvas = document.getElementById(`icon${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 下载所有图标
        function downloadAllIcons() {
            [16, 48, 128].forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }
        
        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图标
            updateIcons();
            
            // 监听控件变化
            ['bgColor', 'iconColor', 'accentColor', 'iconStyle', 'borderRadius', 'shadowIntensity'].forEach(id => {
                document.getElementById(id).addEventListener('change', updateIcons);
                document.getElementById(id).addEventListener('input', updateIcons);
            });
        });
    </script>
</body>
</html>
