/* Money Extension Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 350px;
  min-height: 400px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #f8f9fa;
}

.popup-container {
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  text-align: center;
  position: relative;
}

.popup-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.version {
  font-size: 12px;
  opacity: 0.8;
}

/* Content */
.popup-content {
  flex: 1;
  padding: 20px;
}

.popup-content h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #2c3e50;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 4px;
}

/* Status Section */
.status-section {
  margin-bottom: 24px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: #6c757d;
}

.status-value {
  font-weight: 600;
  color: #495057;
  max-width: 200px;
  text-align: right;
  word-break: break-all;
  font-size: 12px;
}

/* Modules Section */
.modules-section {
  margin-bottom: 24px;
}

.module-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.module-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.module-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.module-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.module-url {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
  margin-bottom: 6px;
}

.module-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  background: #e9ecef;
  color: #6c757d;
}

.module-status.active {
  background: #d4edda;
  color: #155724;
}

/* Actions Section */
.actions-section {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.action-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #667eea;
  color: white;
}

.action-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #6c757d;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.action-btn:active {
  transform: translateY(0);
}

/* Footer */
.popup-footer {
  background: #e9ecef;
  padding: 12px 20px;
  text-align: center;
  margin-top: auto;
}

.footer-text {
  font-size: 12px;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 320px) {
  body {
    width: 300px;
  }
  
  .popup-content {
    padding: 16px;
  }
  
  .status-value {
    max-width: 150px;
  }
}
