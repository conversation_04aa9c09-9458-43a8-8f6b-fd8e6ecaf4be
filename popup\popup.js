/**
 * Money Extension Popup Script
 * 管理插件弹窗的交互逻辑
 */

class PopupManager {
  constructor() {
    this.currentTab = null;
    this.init();
  }

  /**
   * 初始化弹窗
   */
  async init() {
    try {
      // 获取当前活动标签页
      await this.getCurrentTab();
      
      // 更新UI状态
      this.updateUI();
      
      // 绑定事件监听器
      this.bindEventListeners();
      
      console.log('Popup initialized successfully');
    } catch (error) {
      console.error('Popup initialization failed:', error);
    }
  }

  /**
   * 获取当前活动标签页
   */
  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
    } catch (error) {
      console.error('Failed to get current tab:', error);
    }
  }

  /**
   * 更新UI状态
   */
  updateUI() {
    this.updateCurrentUrl();
    this.updateModuleStatus();
  }

  /**
   * 更新当前URL显示
   */
  updateCurrentUrl() {
    const urlElement = document.getElementById('current-url');
    if (this.currentTab && this.currentTab.url) {
      const url = new URL(this.currentTab.url);
      urlElement.textContent = `${url.hostname}${url.pathname}`;
      urlElement.title = this.currentTab.url;
    } else {
      urlElement.textContent = '无法获取';
    }
  }

  /**
   * 更新模块状态
   */
  updateModuleStatus() {
    if (!this.currentTab || !this.currentTab.url) {
      this.setActiveModule('无法检测');
      return;
    }

    const url = this.currentTab.url;
    let activeModule = '无';

    // 检查东方财富模块
    if (this.matchUrl(url, '*://guba.eastmoney.com/rank/*')) {
      activeModule = '东方财富个股人气榜';
      this.setModuleActive('eastmoney_rank', true);
    } else {
      this.setModuleActive('eastmoney_rank', false);
    }

    // 检查财联社模块
    if (this.matchUrl(url, '*://api3.cls.cn/quote/toplist*')) {
      activeModule = '财联社热榜';
      this.setModuleActive('cls_rank', true);
    } else {
      this.setModuleActive('cls_rank', false);
    }

    this.setActiveModule(activeModule);
  }

  /**
   * 设置活动模块显示
   * @param {string} moduleName - 模块名称
   */
  setActiveModule(moduleName) {
    const activeModuleElement = document.getElementById('active-module');
    activeModuleElement.textContent = moduleName;
  }

  /**
   * 设置模块状态
   * @param {string} moduleId - 模块ID
   * @param {boolean} isActive - 是否激活
   */
  setModuleActive(moduleId, isActive) {
    const statusElement = document.querySelector(`[data-module="${moduleId}"]`);
    if (statusElement) {
      statusElement.textContent = isActive ? '已激活' : '未激活';
      statusElement.classList.toggle('active', isActive);
    }
  }

  /**
   * URL匹配工具
   * @param {string} url - 要检查的URL
   * @param {string} pattern - 匹配模式
   * @returns {boolean} 是否匹配
   */
  matchUrl(url, pattern) {
    if (!url || !pattern) return false;
    
    const regex = new RegExp(
      pattern
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.')
    );
    return regex.test(url);
  }

  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    // 刷新按钮
    const refreshBtn = document.getElementById('refresh-btn');
    refreshBtn.addEventListener('click', () => {
      this.handleRefresh();
    });

    // 设置按钮
    const settingsBtn = document.getElementById('settings-btn');
    settingsBtn.addEventListener('click', () => {
      this.handleSettings();
    });
  }

  /**
   * 处理刷新操作
   */
  async handleRefresh() {
    try {
      const refreshBtn = document.getElementById('refresh-btn');
      refreshBtn.textContent = '刷新中...';
      refreshBtn.disabled = true;

      // 重新获取当前标签页信息
      await this.getCurrentTab();
      
      // 更新UI
      this.updateUI();

      // 可以在这里添加与content script的通信，获取更详细的状态信息
      
      setTimeout(() => {
        refreshBtn.textContent = '刷新状态';
        refreshBtn.disabled = false;
      }, 500);

    } catch (error) {
      console.error('Refresh failed:', error);
      const refreshBtn = document.getElementById('refresh-btn');
      refreshBtn.textContent = '刷新失败';
      setTimeout(() => {
        refreshBtn.textContent = '刷新状态';
        refreshBtn.disabled = false;
      }, 1000);
    }
  }

  /**
   * 处理设置操作
   */
  handleSettings() {
    // TODO: 实现设置页面或功能
    alert('设置功能将在后续版本中实现');
  }

  /**
   * 与content script通信
   * @param {Object} message - 消息内容
   * @returns {Promise} 响应结果
   */
  async sendMessageToContentScript(message) {
    try {
      if (!this.currentTab || !this.currentTab.id) {
        throw new Error('No active tab found');
      }

      const response = await chrome.tabs.sendMessage(this.currentTab.id, message);
      return response;
    } catch (error) {
      console.error('Failed to send message to content script:', error);
      throw error;
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
