<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财联社热榜 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .stock-list {
            display: grid;
            gap: 10px;
        }
        
        .stock-item {
            display: grid;
            grid-template-columns: 60px 1fr 100px 100px 100px;
            align-items: center;
            padding: 15px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            transition: all 0.2s ease;
        }
        
        .stock-item:hover {
            background: #f8f9fa;
            border-color: #2196f3;
            cursor: pointer;
        }
        
        .stock-rank {
            font-weight: bold;
            color: #666;
            text-align: center;
        }
        
        .stock-info {
            display: flex;
            flex-direction: column;
        }
        
        .stock-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
            cursor: pointer;
        }
        
        .stock-name:hover {
            color: #2196f3;
        }
        
        .stock-code {
            color: #666;
            font-size: 0.9em;
        }
        
        .stock-price {
            font-weight: bold;
            text-align: right;
            cursor: pointer;
        }
        
        .stock-price:hover {
            color: #2196f3;
        }
        
        .stock-change {
            text-align: right;
            font-weight: bold;
            cursor: pointer;
        }
        
        .stock-change:hover {
            opacity: 0.8;
        }
        
        .stock-change.positive {
            color: #f44336;
        }
        
        .stock-change.negative {
            color: #4caf50;
        }
        
        .stock-volume {
            text-align: right;
            color: #666;
            font-size: 0.9em;
        }
        
        .header {
            display: grid;
            grid-template-columns: 60px 1fr 100px 100px 100px;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
        }
        
        .header-rank {
            text-align: center;
        }
        
        .header-name {
            text-align: left;
        }
        
        .header-price,
        .header-change,
        .header-volume {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>财联社热榜</h1>
        
        <div class="info">
            <strong>测试说明：</strong>这是一个模拟的财联社热榜页面，用于测试Chrome插件的点击跳转功能。
            点击股票名称、现价或涨跌幅应该跳转到对应的东方财富股吧页面。
        </div>
        
        <div class="stock-list">
            <div class="header">
                <div class="header-rank">排名</div>
                <div class="header-name">股票名称</div>
                <div class="header-price">现价</div>
                <div class="header-change">涨跌幅</div>
                <div class="header-volume">成交量</div>
            </div>
            
            <div class="stock-item" data-stock-code="600519">
                <div class="stock-rank">1</div>
                <div class="stock-info">
                    <div class="stock-name">贵州茅台</div>
                    <div class="stock-code">600519</div>
                </div>
                <div class="stock-price">1680.50</div>
                <div class="stock-change positive">+2.35%</div>
                <div class="stock-volume">12.5万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="601318">
                <div class="stock-rank">2</div>
                <div class="stock-info">
                    <div class="stock-name">中国平安</div>
                    <div class="stock-code">601318</div>
                </div>
                <div class="stock-price">45.67</div>
                <div class="stock-change negative">-1.23%</div>
                <div class="stock-volume">89.3万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="600036">
                <div class="stock-rank">3</div>
                <div class="stock-info">
                    <div class="stock-name">招商银行</div>
                    <div class="stock-code">600036</div>
                </div>
                <div class="stock-price">38.92</div>
                <div class="stock-change positive">+0.87%</div>
                <div class="stock-volume">156.7万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="000858">
                <div class="stock-rank">4</div>
                <div class="stock-info">
                    <div class="stock-name">五粮液</div>
                    <div class="stock-code">000858</div>
                </div>
                <div class="stock-price">128.45</div>
                <div class="stock-change positive">+1.56%</div>
                <div class="stock-volume">78.9万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="000001">
                <div class="stock-rank">5</div>
                <div class="stock-info">
                    <div class="stock-name">平安银行</div>
                    <div class="stock-code">000001</div>
                </div>
                <div class="stock-price">12.34</div>
                <div class="stock-change negative">-0.45%</div>
                <div class="stock-volume">234.5万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="600000">
                <div class="stock-rank">6</div>
                <div class="stock-info">
                    <div class="stock-name">浦发银行</div>
                    <div class="stock-code">600000</div>
                </div>
                <div class="stock-price">8.76</div>
                <div class="stock-change positive">+0.23%</div>
                <div class="stock-volume">345.6万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="002415">
                <div class="stock-rank">7</div>
                <div class="stock-info">
                    <div class="stock-name">海康威视</div>
                    <div class="stock-code">002415</div>
                </div>
                <div class="stock-price">32.18</div>
                <div class="stock-change negative">-1.89%</div>
                <div class="stock-volume">123.4万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="000002">
                <div class="stock-rank">8</div>
                <div class="stock-info">
                    <div class="stock-name">万科A</div>
                    <div class="stock-code">000002</div>
                </div>
                <div class="stock-price">15.67</div>
                <div class="stock-change positive">+0.64%</div>
                <div class="stock-volume">567.8万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="600887">
                <div class="stock-rank">9</div>
                <div class="stock-info">
                    <div class="stock-name">伊利股份</div>
                    <div class="stock-code">600887</div>
                </div>
                <div class="stock-price">28.93</div>
                <div class="stock-change negative">-0.34%</div>
                <div class="stock-volume">89.1万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="601166">
                <div class="stock-rank">10</div>
                <div class="stock-info">
                    <div class="stock-name">兴业银行</div>
                    <div class="stock-code">601166</div>
                </div>
                <div class="stock-price">16.45</div>
                <div class="stock-change positive">+1.23%</div>
                <div class="stock-volume">234.7万手</div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试脚本：模拟原始的点击跳转行为
        document.addEventListener('DOMContentLoaded', function() {
            console.log('财联社热榜测试页面已加载');
            
            // 为股票名称、现价、涨跌幅添加原始的点击事件（模拟原网站行为）
            document.querySelectorAll('.stock-name, .stock-price, .stock-change').forEach(element => {
                element.addEventListener('click', function(e) {
                    console.log('原始点击事件被触发:', e.target.textContent);
                    // 这里模拟原网站的跳转行为
                    // 实际的财联社网站可能会跳转到其他页面
                });
            });
        });
    </script>
</body>
</html>
