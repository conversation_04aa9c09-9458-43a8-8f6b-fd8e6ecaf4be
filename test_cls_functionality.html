<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财联社热榜功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .stock-item {
            display: grid;
            grid-template-columns: 60px 1fr 100px 100px 100px;
            align-items: center;
            padding: 15px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin: 10px 0;
            transition: all 0.2s ease;
        }
        
        .stock-item:hover {
            background: #f8f9fa;
            border-color: #2196f3;
        }
        
        .stock-name, .stock-price, .stock-change {
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }
        
        .stock-name:hover, .stock-price:hover, .stock-change:hover {
            background-color: #e3f2fd;
        }
        
        .stock-name {
            font-weight: bold;
            color: #333;
        }
        
        .stock-price {
            font-weight: bold;
            text-align: right;
        }
        
        .stock-change {
            text-align: right;
            font-weight: bold;
        }
        
        .stock-change.positive {
            color: #f44336;
        }
        
        .stock-change.negative {
            color: #4caf50;
        }
        
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>财联社热榜功能测试页面</h1>
        
        <div class="test-section">
            <div class="test-title">1. 插件加载测试</div>
            <button class="test-button" onclick="testPluginLoading()">测试插件加载</button>
            <div id="plugin-test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 股票代码提取测试</div>
            <button class="test-button" onclick="testStockCodeExtraction()">测试股票代码提取</button>
            <div id="extraction-test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 点击跳转测试</div>
            <div class="test-info">
                点击下面的股票名称、现价或涨跌幅，应该会在新标签页中打开对应的东方财富股吧页面。
            </div>
            
            <div class="stock-item" data-stock-code="600519">
                <div>1</div>
                <div class="stock-name">贵州茅台</div>
                <div class="stock-price">1680.50</div>
                <div class="stock-change positive">+2.35%</div>
                <div>12.5万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="601318">
                <div>2</div>
                <div class="stock-name">中国平安</div>
                <div class="stock-price">45.67</div>
                <div class="stock-change negative">-1.23%</div>
                <div>89.3万手</div>
            </div>
            
            <div class="stock-item" data-stock-code="000858">
                <div>3</div>
                <div class="stock-name">五粮液</div>
                <div class="stock-price">128.45</div>
                <div class="stock-change positive">+1.56%</div>
                <div>78.9万手</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. URL构建测试</div>
            <button class="test-button" onclick="testUrlConstruction()">测试URL构建</button>
            <div id="url-test-result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. 手动跳转测试</div>
            <button class="test-button" onclick="manualJumpTest('600519')">跳转到贵州茅台股吧</button>
            <button class="test-button" onclick="manualJumpTest('601318')">跳转到中国平安股吧</button>
            <button class="test-button" onclick="manualJumpTest('000858')">跳转到五粮液股吧</button>
        </div>
    </div>
    
    <script>
        // 测试插件是否加载
        function testPluginLoading() {
            const resultDiv = document.getElementById('plugin-test-result');
            
            // 检查是否有Money Extension相关的脚本或对象
            const hasCommonUtils = typeof window.CommonUtils !== 'undefined';
            const hasClsRankModule = typeof window.ClsRankModule !== 'undefined';
            
            let result = '<div class="test-info">插件加载检查结果:</div>';
            
            if (hasCommonUtils) {
                result += '<div class="test-success">✓ CommonUtils 已加载</div>';
            } else {
                result += '<div class="test-error">✗ CommonUtils 未加载</div>';
            }
            
            if (hasClsRankModule) {
                result += '<div class="test-success">✓ ClsRankModule 已加载</div>';
            } else {
                result += '<div class="test-error">✗ ClsRankModule 未加载</div>';
            }
            
            // 检查控制台是否有相关日志
            result += '<div class="test-info">请检查浏览器控制台是否有 [Money Extension] 相关日志</div>';
            
            resultDiv.innerHTML = result;
        }
        
        // 测试股票代码提取
        function testStockCodeExtraction() {
            const resultDiv = document.getElementById('extraction-test-result');
            
            const testCases = [
                { input: '600519', expected: '600519' },
                { input: 'SH600519', expected: '600519' },
                { input: '贵州茅台 600519', expected: '600519' },
                { input: '000858', expected: '000858' },
                { input: 'invalid', expected: null }
            ];
            
            let result = '<div class="test-info">股票代码提取测试结果:</div>';
            
            testCases.forEach(testCase => {
                const extracted = extractStockCodeFromText(testCase.input);
                const isCorrect = extracted === testCase.expected;
                
                if (isCorrect) {
                    result += `<div class="test-success">✓ "${testCase.input}" → "${extracted}"</div>`;
                } else {
                    result += `<div class="test-error">✗ "${testCase.input}" → "${extracted}" (期望: "${testCase.expected}")</div>`;
                }
            });
            
            resultDiv.innerHTML = result;
        }
        
        // 从文本中提取股票代码（模拟插件功能）
        function extractStockCodeFromText(text) {
            if (!text) return null;
            const match = text.match(/\b([0-9]{6})\b/);
            return match ? match[1] : null;
        }
        
        // 测试URL构建
        function testUrlConstruction() {
            const resultDiv = document.getElementById('url-test-result');
            
            const testCodes = ['600519', '601318', '000858', '002415'];
            
            let result = '<div class="test-info">URL构建测试结果:</div>';
            
            testCodes.forEach(code => {
                const url = `https://guba.eastmoney.com/list,${code}.html`;
                result += `<div class="test-success">✓ ${code} → <a href="${url}" target="_blank">${url}</a></div>`;
            });
            
            resultDiv.innerHTML = result;
        }
        
        // 手动跳转测试
        function manualJumpTest(stockCode) {
            const url = `https://guba.eastmoney.com/list,${stockCode}.html`;
            console.log(`手动跳转测试: ${stockCode} → ${url}`);
            window.open(url, '_blank');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('财联社热榜功能测试页面已加载');
            
            // 模拟为股票元素添加点击事件（如果插件未加载）
            document.querySelectorAll('.stock-name, .stock-price, .stock-change').forEach(element => {
                element.addEventListener('click', function(e) {
                    const stockItem = e.target.closest('.stock-item');
                    const stockCode = stockItem ? stockItem.getAttribute('data-stock-code') : null;
                    
                    if (stockCode) {
                        console.log(`点击了股票元素: ${e.target.textContent.trim()}, 股票代码: ${stockCode}`);
                        
                        // 如果插件已加载，这个事件可能会被插件拦截
                        // 如果插件未加载，这里提供备用的跳转功能
                        if (typeof window.ClsRankModule === 'undefined') {
                            console.log('插件未加载，使用备用跳转功能');
                            manualJumpTest(stockCode);
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>
