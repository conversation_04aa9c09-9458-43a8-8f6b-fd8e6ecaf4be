<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财联社热榜 - 真实结构测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        /* 财联社样式 */
        .p-r { position: relative; }
        .d-f { display: flex; }
        .f-b-s { justify-content: space-between; }
        .f-s-c { align-items: center; }
        .w-100p { width: 100%; }
        .f-s-0 { flex-shrink: 0; }
        .f-g-1 { flex-grow: 1; }
        .m-r-16 { margin-right: 16px; }
        .m-b-8 { margin-bottom: 8px; }
        .m-t-8 { margin-top: 8px; }
        .m-r-5 { margin-right: 5px; }
        .f-s-13 { font-size: 13px; }
        .f-s-16 { font-size: 16px; }
        .f-s-12 { font-size: 12px; }
        .f-w-b { font-weight: bold; }
        .t-a-c { text-align: center; }
        .t-a-r { text-align: right; }
        .l-h-15385 { line-height: 1.5385; }
        .l-h-1375 { line-height: 1.375; }
        .l-h-116667 { line-height: 1.16667; }
        .w-46p { width: 46%; }
        .w-27p { width: 27%; }
        .w-70p { width: 70%; }
        .c-333 { color: #333; }
        .c-666 { color: #666; }
        .c-fb3e4e { color: #fb3e4e; }
        .c-de0422 { color: #de0422; }
        .c-e51532 { color: #e51532; }
        .c-1c2d3e { color: #1c2d3e; }
        .bg-c-fef9f8 { background-color: #fef9f8; }
        .o-h { overflow: hidden; }
        .t-s-083333 { text-shadow: 0.083333px; }
        
        .hot-list-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }
        
        .hot-list-item:hover {
            border-color: #2196f3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }
        
        .hot-number img {
            width: 24px;
            height: 24px;
        }
        
        .hot-stock-label {
            display: inline-block;
            padding: 2px 6px;
            margin-right: 6px;
            border-radius: 3px;
            background: #f0f0f0;
        }
        
        .hot-stock-label-multiple {
            background: #ffe6e6;
        }
        
        .hot-relation {
            padding: 8px;
            border-radius: 4px;
        }
        
        .hot-cailian-icon {
            width: 16px;
            height: 16px;
            margin-right: 5px;
        }
        
        .openapp {
            cursor: pointer;
        }
        
        .openapp:hover .c-333,
        .openapp:hover .c-666,
        .openapp:hover .c-fb3e4e {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>财联社热榜 - 真实结构测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong>这个页面使用了真实的财联社热榜HTML结构。
            点击股票名称、现价或涨跌幅应该跳转到对应的东方财富股吧页面。
        </div>
        
        <!-- 恒宝股份 -->
        <div class="p-r d-f f-b-s w-100p hot-list-item">
            <div class="f-s-0">
                <div class="m-r-16 f-s-13 f-w-b t-a-c l-h-15385 hot-number">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiNmZjQwNDAiLz4KPHRleHQgeD0iMTIiIHk9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgZm9udC13ZWlnaHQ9ImJvbGQiPjE8L3RleHQ+Cjwvc3ZnPgo=">
                </div>
            </div>
            <div class="f-g-1 hot-list-right-box">
                <div class="openapp" data-href="http://a.app.qq.com/o/simple.jsp?pkgname=com.lanjinger.choiassociatedpress&g_f=991653" data-openapplink="cailianpressopen://open?stock_id=sz002104">
                    <div class="m-b-8 d-f f-s-c l-h-1375">
                        <div class="w-46p f-s-16 f-w-b c-333">恒宝股份</div>
                        <div class="w-27p f-s-16 f-w-b c-666">8.91</div>
                        <div class="w-27p f-s-16 f-w-b t-a-r c-fb3e4e">+10.00%</div>
                    </div>
                </div>
                <div class="p-r d-f f-s-c hot-stock-code-wrap" style="height: 16px;">
                    <div class="hot-stock-label-box" style="left:0">
                        <div class="p-r c-de0422 l-h-116667 hot-stock-label hot-stock-label-multiple">
                            <div class="f-s-12 t-s-083333">2天2板</div>
                        </div>
                        <div class="p-r c-666 l-h-116667 hot-stock-label">
                            <div class="f-s-12 t-s-083333">数字货币</div>
                        </div>
                        <div class="p-r c-666 l-h-116667 hot-stock-label">
                            <div class="f-s-12 t-s-083333">eSIM</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 贵州茅台 -->
        <div class="p-r d-f f-b-s w-100p hot-list-item">
            <div class="f-s-0">
                <div class="m-r-16 f-s-13 f-w-b t-a-c l-h-15385 hot-number">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiNmZjQwNDAiLz4KPHRleHQgeD0iMTIiIHk9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgZm9udC13ZWlnaHQ9ImJvbGQiPjI8L3RleHQ+Cjwvc3ZnPgo=">
                </div>
            </div>
            <div class="f-g-1 hot-list-right-box">
                <div class="openapp" data-href="http://a.app.qq.com/o/simple.jsp?pkgname=com.lanjinger.choiassociatedpress&g_f=991653" data-openapplink="cailianpressopen://open?stock_id=sh600519">
                    <div class="m-b-8 d-f f-s-c l-h-1375">
                        <div class="w-46p f-s-16 f-w-b c-333">贵州茅台</div>
                        <div class="w-27p f-s-16 f-w-b c-666">1680.50</div>
                        <div class="w-27p f-s-16 f-w-b t-a-r c-fb3e4e">+2.35%</div>
                    </div>
                </div>
                <div class="p-r d-f f-s-c hot-stock-code-wrap" style="height: 16px;">
                    <div class="hot-stock-label-box" style="left:0">
                        <div class="p-r c-666 l-h-116667 hot-stock-label">
                            <div class="f-s-12 t-s-083333">白酒</div>
                        </div>
                        <div class="p-r c-666 l-h-116667 hot-stock-label">
                            <div class="f-s-12 t-s-083333">消费</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中国平安 -->
        <div class="p-r d-f f-b-s w-100p hot-list-item">
            <div class="f-s-0">
                <div class="m-r-16 f-s-13 f-w-b t-a-c l-h-15385 hot-number">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiNmZjQwNDAiLz4KPHRleHQgeD0iMTIiIHk9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgZm9udC13ZWlnaHQ9ImJvbGQiPjM8L3RleHQ+Cjwvc3ZnPgo=">
                </div>
            </div>
            <div class="f-g-1 hot-list-right-box">
                <div class="openapp" data-href="http://a.app.qq.com/o/simple.jsp?pkgname=com.lanjinger.choiassociatedpress&g_f=991653" data-openapplink="cailianpressopen://open?stock_id=sh601318">
                    <div class="m-b-8 d-f f-s-c l-h-1375">
                        <div class="w-46p f-s-16 f-w-b c-333">中国平安</div>
                        <div class="w-27p f-s-16 f-w-b c-666">45.67</div>
                        <div class="w-27p f-s-16 f-w-b t-a-r c-fb3e4e">-1.23%</div>
                    </div>
                </div>
                <div class="p-r d-f f-s-c hot-stock-code-wrap" style="height: 16px;">
                    <div class="hot-stock-label-box" style="left:0">
                        <div class="p-r c-666 l-h-116667 hot-stock-label">
                            <div class="f-s-12 t-s-083333">保险</div>
                        </div>
                        <div class="p-r c-666 l-h-116667 hot-stock-label">
                            <div class="f-s-12 t-s-083333">金融</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('财联社热榜真实结构测试页面已加载');
            
            // 检查插件是否加载
            setTimeout(() => {
                const hotListItems = document.querySelectorAll('.hot-list-item');
                console.log(`找到 ${hotListItems.length} 个热榜项目`);
                
                hotListItems.forEach((item, index) => {
                    const openAppElement = item.querySelector('[data-openapplink]');
                    if (openAppElement) {
                        const openAppLink = openAppElement.getAttribute('data-openapplink');
                        console.log(`项目 ${index + 1}: ${openAppLink}`);
                    }
                });
            }, 1000);
        });
    </script>
</body>
</html>
