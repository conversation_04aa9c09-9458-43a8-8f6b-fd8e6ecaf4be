<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东方财富人气榜测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            color: #1565c0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: center;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #e3f2fd;
        }
        
        .stock-name {
            text-align: left;
            font-weight: bold;
        }
        
        .stock-code {
            color: #666;
            font-size: 0.9em;
        }
        
        .trend-up {
            color: #28a745;
        }
        
        .trend-down {
            color: #dc3545;
        }
        
        .trend-data {
            font-family: monospace;
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>东方财富人气榜</h1>
        
        <div class="info">
            <strong>测试说明：</strong>这是一个模拟的东方财富人气榜页面，用于测试Chrome插件的"前10率"功能。
            插件会在"历史趋势"列后面添加一个新的"前10率"列。
        </div>
        
        <table id="rankTable">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>股票名称</th>
                    <th>股票代码</th>
                    <th>现价</th>
                    <th>涨跌幅</th>
                    <th>历史趋势</th>
                    <th>人气值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td class="stock-name">贵州茅台</td>
                    <td class="stock-code">600519</td>
                    <td>1680.50</td>
                    <td class="trend-up">+2.35%</td>
                    <td class="trend-data">15,23,8,45,67,12,89,34,56,78</td>
                    <td>98,765</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td class="stock-name">中国平安</td>
                    <td class="stock-code">601318</td>
                    <td>45.67</td>
                    <td class="trend-down">-1.23%</td>
                    <td class="trend-data">45,67,23,89,12,156,234,78,45,123</td>
                    <td>87,432</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td class="stock-name">招商银行</td>
                    <td class="stock-code">600036</td>
                    <td>38.92</td>
                    <td class="trend-up">+0.87%</td>
                    <td class="trend-data">78,45,123,67,89,34,156,23,78,45</td>
                    <td>76,543</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td class="stock-name">五粮液</td>
                    <td class="stock-code">000858</td>
                    <td>156.78</td>
                    <td class="trend-up">+1.45%</td>
                    <td class="trend-data">23,78,45,123,89,67,234,156,78,45</td>
                    <td>65,432</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td class="stock-name">腾讯控股</td>
                    <td class="stock-code">00700</td>
                    <td>345.60</td>
                    <td class="trend-down">-0.65%</td>
                    <td class="trend-data">156,234,78,45,123,67,89,234,156,78</td>
                    <td>54,321</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td class="stock-name">比亚迪</td>
                    <td class="stock-code">002594</td>
                    <td>234.56</td>
                    <td class="trend-up">+3.21%</td>
                    <td class="trend-data">67,89,34,156,78,45,123,234,67,89</td>
                    <td>43,210</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td class="stock-name">宁德时代</td>
                    <td class="stock-code">300750</td>
                    <td>189.34</td>
                    <td class="trend-up">+2.78%</td>
                    <td class="trend-data">34,156,78,45,123,89,67,234,156,78</td>
                    <td>32,109</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td class="stock-name">阿里巴巴</td>
                    <td class="stock-code">09988</td>
                    <td>78.90</td>
                    <td class="trend-down">-1.87%</td>
                    <td class="trend-data">123,67,89,234,156,78,45,123,89,67</td>
                    <td>21,098</td>
                </tr>
                <tr>
                    <td>9</td>
                    <td class="stock-name">美团</td>
                    <td class="stock-code">03690</td>
                    <td>123.45</td>
                    <td class="trend-up">+1.23%</td>
                    <td class="trend-data">89,234,156,78,45,123,67,89,234,156</td>
                    <td>19,876</td>
                </tr>
                <tr>
                    <td>10</td>
                    <td class="stock-name">中芯国际</td>
                    <td class="stock-code">00981</td>
                    <td>15.67</td>
                    <td class="trend-down">-2.34%</td>
                    <td class="trend-data">234,156,78,45,123,89,67,234,156,78</td>
                    <td>18,765</td>
                </tr>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 6px; color: #856404;">
            <strong>注意：</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>历史趋势列中的数字代表过去10天的排名数据</li>
                <li>插件会计算排名在前100的天数比例作为"前10率"</li>
                <li>前10率会用不同颜色显示：绿色(≥80%)、黄色(60-79%)、橙色(40-59%)、红色(<40%)</li>
            </ul>
        </div>
    </div>
    
    <!-- 加载插件脚本进行测试 -->
    <script src="utils/common.js"></script>
    <script src="content/content.js"></script>

    <script>
        // 模拟页面加载完成后的一些动态行为
        document.addEventListener('DOMContentLoaded', function() {
            console.log('东方财富人气榜测试页面加载完成');

            // 模拟一些动态更新
            setTimeout(() => {
                console.log('模拟数据更新...');
                // 这里可以添加一些动态更新表格的逻辑
            }, 2000);

            // 添加测试按钮
            const testButton = document.createElement('button');
            testButton.textContent = '测试插件功能';
            testButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 20px;
                background: #667eea;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                z-index: 9999;
                font-size: 14px;
            `;

            testButton.onclick = function() {
                console.log('手动触发插件测试...');
                if (window.moneyExtension) {
                    window.moneyExtension.loadModuleForCurrentPage();
                } else {
                    console.log('插件未加载，尝试初始化...');
                    // 插件会自动初始化
                }
            };

            document.body.appendChild(testButton);
        });
    </script>
</body>
</html>
