<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Extension - 简化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            color: #1565c0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: center;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #e3f2fd;
        }
        
        .stock-name {
            text-align: left;
            font-weight: bold;
        }
        
        .stock-code {
            color: #666;
            font-size: 0.9em;
        }
        
        .trend-up {
            color: #28a745;
        }
        
        .trend-down {
            color: #dc3545;
        }
        
        .trend-data {
            font-family: monospace;
            font-size: 0.8em;
            color: #666;
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #5a6fd8;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Money Extension - 简化功能测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong>这是一个简化的测试页面，直接测试"前10率"功能的核心逻辑。
            点击下面的按钮来测试不同的功能。
        </div>
        
        <div>
            <button class="test-button" onclick="testAddColumn()">添加前10率列</button>
            <button class="test-button" onclick="testCalculateRates()">计算前10率数据</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <table id="rankTable">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>股票名称</th>
                    <th>股票代码</th>
                    <th>现价</th>
                    <th>涨跌幅</th>
                    <th>历史趋势</th>
                    <th>人气值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td class="stock-name">贵州茅台</td>
                    <td class="stock-code">600519</td>
                    <td>1680.50</td>
                    <td class="trend-up">+2.35%</td>
                    <td>
                        <a class="chart_line" data-strdata="[{&quot;CALCTIME&quot;:&quot;2025-05-22 23:50:00&quot;,&quot;RANK&quot;:15},{&quot;CALCTIME&quot;:&quot;2025-05-23 23:50:00&quot;,&quot;RANK&quot;:23},{&quot;CALCTIME&quot;:&quot;2025-05-24 23:50:00&quot;,&quot;RANK&quot;:8},{&quot;CALCTIME&quot;:&quot;2025-05-25 23:50:00&quot;,&quot;RANK&quot;:45},{&quot;CALCTIME&quot;:&quot;2025-05-26 23:50:00&quot;,&quot;RANK&quot;:67},{&quot;CALCTIME&quot;:&quot;2025-05-27 23:50:00&quot;,&quot;RANK&quot;:12},{&quot;CALCTIME&quot;:&quot;2025-05-28 23:50:00&quot;,&quot;RANK&quot;:89},{&quot;CALCTIME&quot;:&quot;2025-05-29 23:50:00&quot;,&quot;RANK&quot;:34},{&quot;CALCTIME&quot;:&quot;2025-05-30 23:50:00&quot;,&quot;RANK&quot;:56},{&quot;CALCTIME&quot;:&quot;2025-05-31 23:50:00&quot;,&quot;RANK&quot;:78}]" href="#" target="_blank">
                            <div style="position: relative; width: 68px; height: 23px;">
                                <canvas width="68" height="23"></canvas>
                            </div>
                        </a>
                    </td>
                    <td>98,765</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td class="stock-name">中国平安</td>
                    <td class="stock-code">601318</td>
                    <td>45.67</td>
                    <td class="trend-down">-1.23%</td>
                    <td>
                        <a class="chart_line" data-strdata="[{&quot;CALCTIME&quot;:&quot;2025-05-22 23:50:00&quot;,&quot;RANK&quot;:458},{&quot;CALCTIME&quot;:&quot;2025-05-23 23:50:00&quot;,&quot;RANK&quot;:447},{&quot;CALCTIME&quot;:&quot;2025-05-24 23:50:00&quot;,&quot;RANK&quot;:426},{&quot;CALCTIME&quot;:&quot;2025-05-25 23:50:00&quot;,&quot;RANK&quot;:421},{&quot;CALCTIME&quot;:&quot;2025-05-26 23:50:00&quot;,&quot;RANK&quot;:430},{&quot;CALCTIME&quot;:&quot;2025-05-27 23:50:00&quot;,&quot;RANK&quot;:324},{&quot;CALCTIME&quot;:&quot;2025-05-28 23:50:00&quot;,&quot;RANK&quot;:589},{&quot;CALCTIME&quot;:&quot;2025-05-29 23:50:00&quot;,&quot;RANK&quot;:197},{&quot;CALCTIME&quot;:&quot;2025-05-30 23:50:00&quot;,&quot;RANK&quot;:20},{&quot;CALCTIME&quot;:&quot;2025-05-31 23:50:00&quot;,&quot;RANK&quot;:15}]" href="#" target="_blank">
                            <div style="position: relative; width: 68px; height: 23px;">
                                <canvas width="68" height="23"></canvas>
                            </div>
                        </a>
                    </td>
                    <td>87,432</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td class="stock-name">招商银行</td>
                    <td class="stock-code">600036</td>
                    <td>38.92</td>
                    <td class="trend-up">+0.87%</td>
                    <td>
                        <a class="chart_line" data-strdata="[{&quot;CALCTIME&quot;:&quot;2025-05-22 23:50:00&quot;,&quot;RANK&quot;:78},{&quot;CALCTIME&quot;:&quot;2025-05-23 23:50:00&quot;,&quot;RANK&quot;:45},{&quot;CALCTIME&quot;:&quot;2025-05-24 23:50:00&quot;,&quot;RANK&quot;:123},{&quot;CALCTIME&quot;:&quot;2025-05-25 23:50:00&quot;,&quot;RANK&quot;:67},{&quot;CALCTIME&quot;:&quot;2025-05-26 23:50:00&quot;,&quot;RANK&quot;:89},{&quot;CALCTIME&quot;:&quot;2025-05-27 23:50:00&quot;,&quot;RANK&quot;:34},{&quot;CALCTIME&quot;:&quot;2025-05-28 23:50:00&quot;,&quot;RANK&quot;:156},{&quot;CALCTIME&quot;:&quot;2025-05-29 23:50:00&quot;,&quot;RANK&quot;:23},{&quot;CALCTIME&quot;:&quot;2025-05-30 23:50:00&quot;,&quot;RANK&quot;:78},{&quot;CALCTIME&quot;:&quot;2025-05-31 23:50:00&quot;,&quot;RANK&quot;:45}]" href="#" target="_blank">
                            <div style="position: relative; width: 68px; height: 23px;">
                                <canvas width="68" height="23"></canvas>
                            </div>
                        </a>
                    </td>
                    <td>76,543</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td class="stock-name">五粮液</td>
                    <td class="stock-code">000858</td>
                    <td>156.78</td>
                    <td class="trend-up">+1.45%</td>
                    <td>
                        <a class="chart_line" data-strdata="[{&quot;CALCTIME&quot;:&quot;2025-05-22 23:50:00&quot;,&quot;RANK&quot;:23},{&quot;CALCTIME&quot;:&quot;2025-05-23 23:50:00&quot;,&quot;RANK&quot;:78},{&quot;CALCTIME&quot;:&quot;2025-05-24 23:50:00&quot;,&quot;RANK&quot;:45},{&quot;CALCTIME&quot;:&quot;2025-05-25 23:50:00&quot;,&quot;RANK&quot;:123},{&quot;CALCTIME&quot;:&quot;2025-05-26 23:50:00&quot;,&quot;RANK&quot;:89},{&quot;CALCTIME&quot;:&quot;2025-05-27 23:50:00&quot;,&quot;RANK&quot;:67},{&quot;CALCTIME&quot;:&quot;2025-05-28 23:50:00&quot;,&quot;RANK&quot;:234},{&quot;CALCTIME&quot;:&quot;2025-05-29 23:50:00&quot;,&quot;RANK&quot;:156},{&quot;CALCTIME&quot;:&quot;2025-05-30 23:50:00&quot;,&quot;RANK&quot;:78},{&quot;CALCTIME&quot;:&quot;2025-05-31 23:50:00&quot;,&quot;RANK&quot;:45}]" href="#" target="_blank">
                            <div style="position: relative; width: 68px; height: 23px;">
                                <canvas width="68" height="23"></canvas>
                            </div>
                        </a>
                    </td>
                    <td>65,432</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td class="stock-name">腾讯控股</td>
                    <td class="stock-code">00700</td>
                    <td>345.60</td>
                    <td class="trend-down">-0.65%</td>
                    <td>
                        <a class="chart_line" data-strdata="[{&quot;CALCTIME&quot;:&quot;2025-05-22 23:50:00&quot;,&quot;RANK&quot;:156},{&quot;CALCTIME&quot;:&quot;2025-05-23 23:50:00&quot;,&quot;RANK&quot;:234},{&quot;CALCTIME&quot;:&quot;2025-05-24 23:50:00&quot;,&quot;RANK&quot;:78},{&quot;CALCTIME&quot;:&quot;2025-05-25 23:50:00&quot;,&quot;RANK&quot;:45},{&quot;CALCTIME&quot;:&quot;2025-05-26 23:50:00&quot;,&quot;RANK&quot;:123},{&quot;CALCTIME&quot;:&quot;2025-05-27 23:50:00&quot;,&quot;RANK&quot;:67},{&quot;CALCTIME&quot;:&quot;2025-05-28 23:50:00&quot;,&quot;RANK&quot;:89},{&quot;CALCTIME&quot;:&quot;2025-05-29 23:50:00&quot;,&quot;RANK&quot;:234},{&quot;CALCTIME&quot;:&quot;2025-05-30 23:50:00&quot;,&quot;RANK&quot;:156},{&quot;CALCTIME&quot;:&quot;2025-05-31 23:50:00&quot;,&quot;RANK&quot;:78}]" href="#" target="_blank">
                            <div style="position: relative; width: 68px; height: 23px;">
                                <canvas width="68" height="23"></canvas>
                            </div>
                        </a>
                    </td>
                    <td>54,321</td>
                </tr>
            </tbody>
        </table>
        
        <div class="log-area" id="logArea">
            <div><strong>测试日志：</strong></div>
        </div>
    </div>

    <script>
        // 简化的日志函数
        function log(message, data = '') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message} ${data ? JSON.stringify(data) : ''}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            const logArea = document.getElementById('logArea');
            logArea.innerHTML = '<div><strong>测试日志：</strong></div>';
        }
        
        // 测试添加列功能
        function testAddColumn() {
            log('开始测试添加前10率列...');
            
            const table = document.getElementById('rankTable');
            const headerRow = table.querySelector('thead tr');
            
            // 检查是否已经添加过
            if (headerRow.querySelector('[data-test="top10-rate-header"]')) {
                log('前10率列已存在，跳过添加');
                return;
            }
            
            // 创建表头
            const headerCell = document.createElement('th');
            headerCell.textContent = '前10率';
            headerCell.style.cssText = `
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px 12px;
                text-align: center;
                font-weight: bold;
                color: #495057;
                white-space: nowrap;
                min-width: 80px;
            `;
            headerCell.setAttribute('data-test', 'top10-rate-header');
            headerCell.title = '过去10天内排名在前100的天数比例';
            
            // 插入到历史趋势列之后
            const trendHeader = Array.from(headerRow.children).find(th => 
                th.textContent.includes('历史趋势')
            );
            
            if (trendHeader) {
                trendHeader.insertAdjacentElement('afterend', headerCell);
                log('表头添加成功，位置：历史趋势列之后');
            } else {
                headerRow.appendChild(headerCell);
                log('表头添加成功，位置：表格末尾');
            }
        }
        
        // 测试计算前10率数据
        function testCalculateRates() {
            log('开始计算前10率数据...');
            
            const table = document.getElementById('rankTable');
            const dataRows = table.querySelectorAll('tbody tr');
            
            dataRows.forEach((row, index) => {
                // 检查是否已经添加过数据
                if (row.querySelector('[data-test="top10-rate-data"]')) {
                    log(`第${index + 1}行数据已存在，跳过计算`);
                    return;
                }
                
                // 查找历史趋势数据
                const trendCell = findTrendCell(row);
                if (!trendCell) {
                    log(`第${index + 1}行：未找到历史趋势数据`);
                    return;
                }
                
                // 提取排名数据
                const rankData = extractRankData(trendCell);
                log(`第${index + 1}行排名数据:`, rankData);
                
                // 计算前10率
                const top10Rate = calculateTop10Rate(rankData);

                // 获取股票名称
                const stockName = getStockNameFromRow(row);

                // 详细分析数据
                const top100Days = rankData.filter(rank => rank > 0 && rank <= 100);
                const top50Days = rankData.filter(rank => rank > 0 && rank <= 50);
                const top10Days = rankData.filter(rank => rank > 0 && rank <= 10);

                log(`${stockName} 详细分析:`, {
                    '排名数据': rankData,
                    '总天数': rankData.length,
                    '前100名天数': top100Days.length,
                    '前50名天数': top50Days.length,
                    '前10名天数': top10Days.length,
                    '前100率': top10Rate + '%',
                    '最高排名': Math.min(...rankData),
                    '最低排名': Math.max(...rankData),
                    '平均排名': Math.round(rankData.reduce((a, b) => a + b, 0) / rankData.length)
                });

                // 创建数据单元格
                const dataCell = document.createElement('td');
                dataCell.style.cssText = `
                    border: 1px solid #dee2e6;
                    padding: 8px 12px;
                    text-align: center;
                    vertical-align: middle;
                    background-color: #fff;
                `;
                dataCell.setAttribute('data-test', 'top10-rate-data');

                // 设置内容和样式
                setDataCellContent(dataCell, top10Rate, rankData);
                
                // 插入到合适位置
                const trendCellIndex = Array.from(row.children).indexOf(trendCell);
                if (trendCellIndex >= 0 && trendCellIndex < row.children.length - 1) {
                    row.children[trendCellIndex].insertAdjacentElement('afterend', dataCell);
                } else {
                    row.appendChild(dataCell);
                }
            });
            
            log('前10率数据计算完成！');
        }
        
        // 查找历史趋势单元格
        function findTrendCell(row) {
            const cells = row.querySelectorAll('td');
            
            // 通过表头找到对应的列索引
            const table = row.closest('table');
            const headerRow = table.querySelector('thead tr');
            const headerCells = headerRow.querySelectorAll('th');
            
            for (let i = 0; i < headerCells.length; i++) {
                const headerText = headerCells[i].textContent.trim();
                if (headerText.includes('历史趋势')) {
                    return cells[i] || null;
                }
            }
            
            return null;
        }
        
        // 从趋势单元格提取排名数据
        function extractRankData(trendCell) {
            try {
                // 方法1: 从data-strdata属性中提取JSON数据
                const chartLink = trendCell.querySelector('a.chart_line');
                if (chartLink) {
                    const strData = chartLink.getAttribute('data-strdata');
                    if (strData) {
                        log('找到data-strdata属性，开始解析JSON数据');

                        try {
                            // 解码HTML实体
                            const decodedData = strData.replace(/&quot;/g, '"');
                            const rankDataArray = JSON.parse(decodedData);

                            if (Array.isArray(rankDataArray)) {
                                // 提取最近10天的排名数据
                                const ranks = rankDataArray
                                    .filter(item => item.RANK && typeof item.RANK === 'number')
                                    .slice(-10) // 取最后10条记录
                                    .map(item => item.RANK);

                                log('成功从JSON中提取排名数据:', ranks);
                                return ranks;
                            }
                        } catch (jsonError) {
                            log('JSON解析失败:', jsonError);
                        }
                    }
                }

                // 方法2: 从文本中提取数字（备用方案）
                const text = trendCell.textContent || '';
                const numbers = text.match(/\d+/g);
                if (numbers) {
                    const ranks = numbers.map(n => parseInt(n)).filter(n => n > 0 && n <= 10000).slice(0, 10);
                    if (ranks.length > 0) {
                        log('从文本中提取到数字:', ranks);
                        return ranks;
                    }
                }

                log('未找到有效的排名数据');
                return [];

            } catch (error) {
                log('提取排名数据时发生错误:', error);
                return [];
            }
        }
        
        // 计算前10率
        function calculateTop10Rate(rankData) {
            if (!rankData || rankData.length === 0) return -1;
            
            const top100Days = rankData.filter(rank => rank > 0 && rank <= 100).length;
            const totalDays = Math.min(rankData.length, 10);
            
            if (totalDays === 0) return -1;
            
            return Math.round((top100Days / totalDays) * 100);
        }
        
        // 获取股票名称
        function getStockNameFromRow(row) {
            if (!row) return '未知股票';

            const cells = row.querySelectorAll('td');

            // 尝试多种方式查找股票名称
            for (const cell of cells) {
                const text = cell.textContent.trim();

                // 跳过数字、百分比、空值
                if (!text || /^\d+$/.test(text) || /^[+-]?\d+\.?\d*%?$/.test(text) || text === '--') {
                    continue;
                }

                // 如果包含中文字符，很可能是股票名称
                if (/[\u4e00-\u9fa5]/.test(text)) {
                    return text;
                }
            }

            // 如果没找到中文名称，返回第一个非数字文本
            for (const cell of cells) {
                const text = cell.textContent.trim();
                if (text && !/^\d+$/.test(text) && !/^[+-]?\d+\.?\d*%?$/.test(text) && text !== '--') {
                    return text;
                }
            }

            return '未知股票';
        }

        // 设置数据单元格内容
        function setDataCellContent(cell, rate, rankData = null) {
            if (rate === -1) {
                cell.textContent = '--';
                cell.style.color = '#6c757d';
                cell.title = '暂无数据';
            } else {
                cell.textContent = `${rate}%`;

                // 根据比例设置颜色
                if (rate >= 80) {
                    cell.style.color = '#28a745'; // 绿色 - 优秀
                    cell.style.fontWeight = 'bold';
                } else if (rate >= 60) {
                    cell.style.color = '#ffc107'; // 黄色 - 良好
                } else if (rate >= 40) {
                    cell.style.color = '#fd7e14'; // 橙色 - 一般
                } else {
                    cell.style.color = '#dc3545'; // 红色 - 较差
                }

                // 增强的提示信息
                if (rankData && rankData.length > 0) {
                    const top100Days = rankData.filter(rank => rank > 0 && rank <= 100).length;
                    const minRank = Math.min(...rankData);
                    const maxRank = Math.max(...rankData);
                    cell.title = `过去${rankData.length}天内有${top100Days}天排名在前100\n最高排名: ${minRank}\n最低排名: ${maxRank}\n排名数据: [${rankData.join(', ')}]`;
                } else {
                    cell.title = `过去10天内有${Math.round(rate/10)}天排名在前100`;
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('简化测试页面加载完成');
            log('点击按钮开始测试功能...');
        });
    </script>
</body>
</html>
