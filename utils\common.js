/**
 * 通用工具函数库
 * 提供插件中各模块共用的工具方法
 */

class CommonUtils {
  /**
   * 日志输出工具
   * @param {string} level - 日志级别 (info, warn, error)
   * @param {string} module - 模块名称
   * @param {string} message - 日志信息
   * @param {any} data - 附加数据
   */
  static log(level, module, message, data = null) {
    const timestamp = new Date().toISOString();
    const prefix = `[Money Extension][${timestamp}][${module}]`;
    
    switch (level) {
      case 'info':
        console.log(`${prefix} ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
      default:
        console.log(`${prefix} ${message}`, data || '');
    }
  }

  /**
   * URL匹配工具
   * @param {string} url - 要检查的URL
   * @param {string|RegExp|Array} patterns - 匹配模式
   * @returns {boolean} 是否匹配
   */
  static matchUrl(url, patterns) {
    if (!url || !patterns) return false;
    
    // 如果patterns是数组，检查是否有任一匹配
    if (Array.isArray(patterns)) {
      return patterns.some(pattern => this.matchUrl(url, pattern));
    }
    
    // 如果是正则表达式
    if (patterns instanceof RegExp) {
      return patterns.test(url);
    }
    
    // 如果是字符串，转换为简单的通配符匹配
    if (typeof patterns === 'string') {
      const regex = new RegExp(
        patterns
          .replace(/\*/g, '.*')
          .replace(/\?/g, '.')
      );
      return regex.test(url);
    }
    
    return false;
  }

  /**
   * DOM元素等待工具
   * @param {string} selector - CSS选择器
   * @param {number} timeout - 超时时间(毫秒)
   * @param {Element} parent - 父元素，默认为document
   * @returns {Promise<Element>} 找到的元素
   */
  static waitForElement(selector, timeout = 5000, parent = document) {
    return new Promise((resolve, reject) => {
      const element = parent.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = parent.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(parent, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} wait - 等待时间
   * @returns {Function} 防抖后的函数
   */
  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} limit - 时间限制
   * @returns {Function} 节流后的函数
   */
  static throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

// 全局可用
window.CommonUtils = CommonUtils;
